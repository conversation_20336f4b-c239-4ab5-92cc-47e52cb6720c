CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/_not-found"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/_not-found/layout", "zTpDe0-OIJPkmH6HS4i5Z/_not-found"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/_not-found/page", "zTpDe0-OIJPkmH6HS4i5Z/_not-found"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/_not-found", "zTpDe0-OIJPkmH6HS4i5Z/_not-found"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/account-activation"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/account-activation/layout", "zTpDe0-OIJPkmH6HS4i5Z/account-activation"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/account-activation/page", "zTpDe0-OIJPkmH6HS4i5Z/account-activation"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/account-activation", "zTpDe0-OIJPkmH6HS4i5Z/account-activation"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin/brush/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin/brush/page", "zTpDe0-OIJPkmH6HS4i5Z/admin/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin/brush", "zTpDe0-OIJPkmH6HS4i5Z/admin/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin-mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin-mobile-test/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin-mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin-mobile-test/page", "zTpDe0-OIJPkmH6HS4i5Z/admin-mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin-mobile-test", "zTpDe0-OIJPkmH6HS4i5Z/admin-mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin/layout", "zTpDe0-OIJPkmH6HS4i5Z/admin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin/page", "zTpDe0-OIJPkmH6HS4i5Z/admin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/admin", "zTpDe0-OIJPkmH6HS4i5Z/admin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/error"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/error"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/error/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/error"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/error/page", "zTpDe0-OIJPkmH6HS4i5Z/auth/error"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/error", "zTpDe0-OIJPkmH6HS4i5Z/auth/error"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/register"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/register"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/register/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/register"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/register/page", "zTpDe0-OIJPkmH6HS4i5Z/auth/register"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/register", "zTpDe0-OIJPkmH6HS4i5Z/auth/register"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/signin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/signin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/signin/layout", "zTpDe0-OIJPkmH6HS4i5Z/auth/signin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/signin/page", "zTpDe0-OIJPkmH6HS4i5Z/auth/signin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/auth/signin", "zTpDe0-OIJPkmH6HS4i5Z/auth/signin"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/brush/layout", "zTpDe0-OIJPkmH6HS4i5Z/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/brush/page", "zTpDe0-OIJPkmH6HS4i5Z/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/brush", "zTpDe0-OIJPkmH6HS4i5Z/brush"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/download"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/download/layout", "zTpDe0-OIJPkmH6HS4i5Z/download"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/download/page", "zTpDe0-OIJPkmH6HS4i5Z/download"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/download", "zTpDe0-OIJPkmH6HS4i5Z/download"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/favicon.ico"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/favicon.ico/layout", "zTpDe0-OIJPkmH6HS4i5Z/favicon.ico"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/favicon.ico/route", "zTpDe0-OIJPkmH6HS4i5Z/favicon.ico"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/favicon.ico", "zTpDe0-OIJPkmH6HS4i5Z/favicon.ico"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/generate-icons"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/generate-icons/layout", "zTpDe0-OIJPkmH6HS4i5Z/generate-icons"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/generate-icons/page", "zTpDe0-OIJPkmH6HS4i5Z/generate-icons"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/generate-icons", "zTpDe0-OIJPkmH6HS4i5Z/generate-icons"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/generate-screenshots"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/generate-screenshots/layout", "zTpDe0-OIJPkmH6HS4i5Z/generate-screenshots"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/generate-screenshots/page", "zTpDe0-OIJPkmH6HS4i5Z/generate-screenshots"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/generate-screenshots", "zTpDe0-OIJPkmH6HS4i5Z/generate-screenshots"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/index"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/page", "zTpDe0-OIJPkmH6HS4i5Z/index"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/", "zTpDe0-OIJPkmH6HS4i5Z/index"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/mobile-test/layout", "zTpDe0-OIJPkmH6HS4i5Z/mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/mobile-test/page", "zTpDe0-OIJPkmH6HS4i5Z/mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/mobile-test", "zTpDe0-OIJPkmH6HS4i5Z/mobile-test"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/network-status"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/network-status/layout", "zTpDe0-OIJPkmH6HS4i5Z/network-status"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/network-status/page", "zTpDe0-OIJPkmH6HS4i5Z/network-status"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/network-status", "zTpDe0-OIJPkmH6HS4i5Z/network-status"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/pwa-debug"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/pwa-debug/layout", "zTpDe0-OIJPkmH6HS4i5Z/pwa-debug"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/pwa-debug/page", "zTpDe0-OIJPkmH6HS4i5Z/pwa-debug"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/pwa-debug", "zTpDe0-OIJPkmH6HS4i5Z/pwa-debug"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/security-notice"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/security-notice/layout", "zTpDe0-OIJPkmH6HS4i5Z/security-notice"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/security-notice/page", "zTpDe0-OIJPkmH6HS4i5Z/security-notice"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/security-notice", "zTpDe0-OIJPkmH6HS4i5Z/security-notice"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-balance-display"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-balance-display/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-balance-display"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-balance-display/page", "zTpDe0-OIJPkmH6HS4i5Z/test-balance-display"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-balance-display", "zTpDe0-OIJPkmH6HS4i5Z/test-balance-display"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-balance/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-balance/page", "zTpDe0-OIJPkmH6HS4i5Z/test-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-balance", "zTpDe0-OIJPkmH6HS4i5Z/test-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-brush-modal"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-brush-modal/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-brush-modal"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-brush-modal/page", "zTpDe0-OIJPkmH6HS4i5Z/test-brush-modal"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-brush-modal", "zTpDe0-OIJPkmH6HS4i5Z/test-brush-modal"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-fixes"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-fixes/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-fixes"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-fixes/page", "zTpDe0-OIJPkmH6HS4i5Z/test-fixes"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-fixes", "zTpDe0-OIJPkmH6HS4i5Z/test-fixes"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-nile-complete"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-nile-complete/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-nile-complete"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-nile-complete/page", "zTpDe0-OIJPkmH6HS4i5Z/test-nile-complete"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-nile-complete", "zTpDe0-OIJPkmH6HS4i5Z/test-nile-complete"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-usdt-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-usdt-balance/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-usdt-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-usdt-balance/page", "zTpDe0-OIJPkmH6HS4i5Z/test-usdt-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-usdt-balance", "zTpDe0-OIJPkmH6HS4i5Z/test-usdt-balance"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-zoom"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-zoom/layout", "zTpDe0-OIJPkmH6HS4i5Z/test-zoom"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-zoom/page", "zTpDe0-OIJPkmH6HS4i5Z/test-zoom"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/test-zoom", "zTpDe0-OIJPkmH6HS4i5Z/test-zoom"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/wallet-password-help"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/wallet-password-help/layout", "zTpDe0-OIJPkmH6HS4i5Z/wallet-password-help"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/wallet-password-help/page", "zTpDe0-OIJPkmH6HS4i5Z/wallet-password-help"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/wallet-password-help", "zTpDe0-OIJPkmH6HS4i5Z/wallet-password-help"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/layout", "zTpDe0-OIJPkmH6HS4i5Z/wallet"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/wallet/layout", "zTpDe0-OIJPkmH6HS4i5Z/wallet"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/wallet/page", "zTpDe0-OIJPkmH6HS4i5Z/wallet"), ("zTpDe0-OIJPkmH6HS4i5Z/_N_T_/wallet", "zTpDe0-OIJPkmH6HS4i5Z/wallet");