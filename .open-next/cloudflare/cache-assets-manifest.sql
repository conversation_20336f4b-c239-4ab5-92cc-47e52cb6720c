CREATE TABLE IF NOT EXISTS tags (tag TEXT NOT NULL, path TEXT NOT NULL, UNIQUE(tag, path) ON CONFLICT REPLACE);
     CREATE TABLE IF NOT EXISTS revalidations (tag TEXT NOT NULL, revalidatedAt INTEGER NOT NULL, UNIQUE(tag) ON CONFLICT REPLACE);
INSERT INTO tags (tag, path) VALUES ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/_not-found"), ("nonj1mvarj3O2pai6IkIh/_N_T_/_not-found/layout", "nonj1mvarj3O2pai6IkIh/_not-found"), ("nonj1mvarj3O2pai6IkIh/_N_T_/_not-found/page", "nonj1mvarj3O2pai6IkIh/_not-found"), ("nonj1mvarj3O2pai6IkIh/_N_T_/_not-found", "nonj1mvarj3O2pai6IkIh/_not-found"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/account-activation"), ("nonj1mvarj3O2pai6IkIh/_N_T_/account-activation/layout", "nonj1mvarj3O2pai6IkIh/account-activation"), ("nonj1mvarj3O2pai6IkIh/_N_T_/account-activation/page", "nonj1mvarj3O2pai6IkIh/account-activation"), ("nonj1mvarj3O2pai6IkIh/_N_T_/account-activation", "nonj1mvarj3O2pai6IkIh/account-activation"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/admin/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin/layout", "nonj1mvarj3O2pai6IkIh/admin/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin/brush/layout", "nonj1mvarj3O2pai6IkIh/admin/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin/brush/page", "nonj1mvarj3O2pai6IkIh/admin/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin/brush", "nonj1mvarj3O2pai6IkIh/admin/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/admin-mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin-mobile-test/layout", "nonj1mvarj3O2pai6IkIh/admin-mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin-mobile-test/page", "nonj1mvarj3O2pai6IkIh/admin-mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin-mobile-test", "nonj1mvarj3O2pai6IkIh/admin-mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/admin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin/layout", "nonj1mvarj3O2pai6IkIh/admin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin/page", "nonj1mvarj3O2pai6IkIh/admin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/admin", "nonj1mvarj3O2pai6IkIh/admin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/auth/error"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/layout", "nonj1mvarj3O2pai6IkIh/auth/error"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/error/layout", "nonj1mvarj3O2pai6IkIh/auth/error"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/error/page", "nonj1mvarj3O2pai6IkIh/auth/error"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/error", "nonj1mvarj3O2pai6IkIh/auth/error"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/auth/register"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/layout", "nonj1mvarj3O2pai6IkIh/auth/register"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/register/layout", "nonj1mvarj3O2pai6IkIh/auth/register"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/register/page", "nonj1mvarj3O2pai6IkIh/auth/register"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/register", "nonj1mvarj3O2pai6IkIh/auth/register"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/auth/signin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/layout", "nonj1mvarj3O2pai6IkIh/auth/signin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/signin/layout", "nonj1mvarj3O2pai6IkIh/auth/signin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/signin/page", "nonj1mvarj3O2pai6IkIh/auth/signin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/auth/signin", "nonj1mvarj3O2pai6IkIh/auth/signin"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/brush/layout", "nonj1mvarj3O2pai6IkIh/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/brush/page", "nonj1mvarj3O2pai6IkIh/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/brush", "nonj1mvarj3O2pai6IkIh/brush"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/download"), ("nonj1mvarj3O2pai6IkIh/_N_T_/download/layout", "nonj1mvarj3O2pai6IkIh/download"), ("nonj1mvarj3O2pai6IkIh/_N_T_/download/page", "nonj1mvarj3O2pai6IkIh/download"), ("nonj1mvarj3O2pai6IkIh/_N_T_/download", "nonj1mvarj3O2pai6IkIh/download"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/favicon.ico"), ("nonj1mvarj3O2pai6IkIh/_N_T_/favicon.ico/layout", "nonj1mvarj3O2pai6IkIh/favicon.ico"), ("nonj1mvarj3O2pai6IkIh/_N_T_/favicon.ico/route", "nonj1mvarj3O2pai6IkIh/favicon.ico"), ("nonj1mvarj3O2pai6IkIh/_N_T_/favicon.ico", "nonj1mvarj3O2pai6IkIh/favicon.ico"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/generate-icons"), ("nonj1mvarj3O2pai6IkIh/_N_T_/generate-icons/layout", "nonj1mvarj3O2pai6IkIh/generate-icons"), ("nonj1mvarj3O2pai6IkIh/_N_T_/generate-icons/page", "nonj1mvarj3O2pai6IkIh/generate-icons"), ("nonj1mvarj3O2pai6IkIh/_N_T_/generate-icons", "nonj1mvarj3O2pai6IkIh/generate-icons"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/generate-screenshots"), ("nonj1mvarj3O2pai6IkIh/_N_T_/generate-screenshots/layout", "nonj1mvarj3O2pai6IkIh/generate-screenshots"), ("nonj1mvarj3O2pai6IkIh/_N_T_/generate-screenshots/page", "nonj1mvarj3O2pai6IkIh/generate-screenshots"), ("nonj1mvarj3O2pai6IkIh/_N_T_/generate-screenshots", "nonj1mvarj3O2pai6IkIh/generate-screenshots"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/index"), ("nonj1mvarj3O2pai6IkIh/_N_T_/page", "nonj1mvarj3O2pai6IkIh/index"), ("nonj1mvarj3O2pai6IkIh/_N_T_/", "nonj1mvarj3O2pai6IkIh/index"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/mobile-test/layout", "nonj1mvarj3O2pai6IkIh/mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/mobile-test/page", "nonj1mvarj3O2pai6IkIh/mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/mobile-test", "nonj1mvarj3O2pai6IkIh/mobile-test"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/network-status"), ("nonj1mvarj3O2pai6IkIh/_N_T_/network-status/layout", "nonj1mvarj3O2pai6IkIh/network-status"), ("nonj1mvarj3O2pai6IkIh/_N_T_/network-status/page", "nonj1mvarj3O2pai6IkIh/network-status"), ("nonj1mvarj3O2pai6IkIh/_N_T_/network-status", "nonj1mvarj3O2pai6IkIh/network-status"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/pwa-debug"), ("nonj1mvarj3O2pai6IkIh/_N_T_/pwa-debug/layout", "nonj1mvarj3O2pai6IkIh/pwa-debug"), ("nonj1mvarj3O2pai6IkIh/_N_T_/pwa-debug/page", "nonj1mvarj3O2pai6IkIh/pwa-debug"), ("nonj1mvarj3O2pai6IkIh/_N_T_/pwa-debug", "nonj1mvarj3O2pai6IkIh/pwa-debug"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/security-notice"), ("nonj1mvarj3O2pai6IkIh/_N_T_/security-notice/layout", "nonj1mvarj3O2pai6IkIh/security-notice"), ("nonj1mvarj3O2pai6IkIh/_N_T_/security-notice/page", "nonj1mvarj3O2pai6IkIh/security-notice"), ("nonj1mvarj3O2pai6IkIh/_N_T_/security-notice", "nonj1mvarj3O2pai6IkIh/security-notice"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-balance-display"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-balance-display/layout", "nonj1mvarj3O2pai6IkIh/test-balance-display"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-balance-display/page", "nonj1mvarj3O2pai6IkIh/test-balance-display"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-balance-display", "nonj1mvarj3O2pai6IkIh/test-balance-display"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-balance/layout", "nonj1mvarj3O2pai6IkIh/test-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-balance/page", "nonj1mvarj3O2pai6IkIh/test-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-balance", "nonj1mvarj3O2pai6IkIh/test-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-brush-modal"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-brush-modal/layout", "nonj1mvarj3O2pai6IkIh/test-brush-modal"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-brush-modal/page", "nonj1mvarj3O2pai6IkIh/test-brush-modal"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-brush-modal", "nonj1mvarj3O2pai6IkIh/test-brush-modal"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-fixes"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-fixes/layout", "nonj1mvarj3O2pai6IkIh/test-fixes"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-fixes/page", "nonj1mvarj3O2pai6IkIh/test-fixes"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-fixes", "nonj1mvarj3O2pai6IkIh/test-fixes"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-nile-complete"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-nile-complete/layout", "nonj1mvarj3O2pai6IkIh/test-nile-complete"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-nile-complete/page", "nonj1mvarj3O2pai6IkIh/test-nile-complete"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-nile-complete", "nonj1mvarj3O2pai6IkIh/test-nile-complete"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-usdt-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-usdt-balance/layout", "nonj1mvarj3O2pai6IkIh/test-usdt-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-usdt-balance/page", "nonj1mvarj3O2pai6IkIh/test-usdt-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-usdt-balance", "nonj1mvarj3O2pai6IkIh/test-usdt-balance"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/test-zoom"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-zoom/layout", "nonj1mvarj3O2pai6IkIh/test-zoom"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-zoom/page", "nonj1mvarj3O2pai6IkIh/test-zoom"), ("nonj1mvarj3O2pai6IkIh/_N_T_/test-zoom", "nonj1mvarj3O2pai6IkIh/test-zoom"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/wallet-password-help"), ("nonj1mvarj3O2pai6IkIh/_N_T_/wallet-password-help/layout", "nonj1mvarj3O2pai6IkIh/wallet-password-help"), ("nonj1mvarj3O2pai6IkIh/_N_T_/wallet-password-help/page", "nonj1mvarj3O2pai6IkIh/wallet-password-help"), ("nonj1mvarj3O2pai6IkIh/_N_T_/wallet-password-help", "nonj1mvarj3O2pai6IkIh/wallet-password-help"), ("nonj1mvarj3O2pai6IkIh/_N_T_/layout", "nonj1mvarj3O2pai6IkIh/wallet"), ("nonj1mvarj3O2pai6IkIh/_N_T_/wallet/layout", "nonj1mvarj3O2pai6IkIh/wallet"), ("nonj1mvarj3O2pai6IkIh/_N_T_/wallet/page", "nonj1mvarj3O2pai6IkIh/wallet"), ("nonj1mvarj3O2pai6IkIh/_N_T_/wallet", "nonj1mvarj3O2pai6IkIh/wallet");