// TRON钱包服务 - 钱包创建、导入、管理功能
const { TronWeb } = require('tronweb');
import {  TRON_NETWORKS, USDT_CONTRACTS } from './tronweb';

// 钱包创建和管理接口
export interface WalletCreateOptions {
  name: string;
  network?: 'mainnet' | 'nile' | 'shasta';
}

export interface WalletImportOptions {
  name: string;
  privateKey: string;
  network?: 'mainnet' | 'nile' | 'shasta';
}

export interface WalletInfo {
  address: string;
  privateKey?: string; // 仅在创建时返回，不存储
  name: string;
  network: string;
}

export interface WalletBalance {
  TRX: number;
  USDT: number;
}

export interface TransferOptions {
  fromAddress: string;
  toAddress: string;
  amount: number;
  currency: 'TRX' | 'USDT';
  privateKey?: string; // 用于签名交易
}

export interface TransferResult {
  success: boolean;
  txHash?: string;
  error?: string;
  amount: number;
  currency: string;
  fromAddress: string;
  toAddress: string;
}

export class WalletService {
  private tronWeb: any;
  private network: 'mainnet' | 'nile' | 'shasta';

  constructor(network: 'mainnet' | 'nile' | 'shasta' = 'mainnet') {
    this.network = network;
    this.initializeTronWeb();
  }

  private getApiKeyForNetwork(): string | undefined {
    switch (this.network) {
      case 'mainnet':
        return process.env.TRON_API_KEY || process.env.NEXT_PUBLIC_TRON_API_KEY || '3f67c2cc-9119-468b-a336-f3f95b7bfec3';
      case 'nile':
        return process.env.TRON_NILE_API_KEY || process.env.NEXT_PUBLIC_TRON_NILE_API_KEY;
      case 'shasta':
        return process.env.TRON_SHASTA_API_KEY || process.env.NEXT_PUBLIC_TRON_SHASTA_API_KEY;
      default:
        return undefined;
    }
  }

  private initializeTronWeb() {
    const networkConfig = TRON_NETWORKS[this.network];
    const apiKey = this.getApiKeyForNetwork();

    console.log(`初始化TronWeb - 网络: ${this.network}, API密钥: ${apiKey ? '已设置' : '未设置'}`);

    const headers: Record<string, string> = {
      "Content-Type": "application/json"
    };

    if (apiKey) {
      headers["TRON-PRO-API-KEY"] = apiKey;
    }

    this.tronWeb = new TronWeb({
      fullHost: networkConfig.fullHost,
      headers: headers,
      privateKey: '01' // 临时私钥，实际使用时会替换
    });
  }

  // 创建新钱包
  async createWallet(options: WalletCreateOptions): Promise<WalletInfo> {
    try {
      // 生成新的钱包地址和私钥
      const account = await this.tronWeb.createAccount();
      
      return {
        address: account.address.base58,
        privateKey: account.privateKey,
        name: options.name,
        network: options.network || this.network
      };
    } catch (error: any) {
      throw new Error(`创建钱包失败: ${error.message}`);
    }
  }

  // 导入钱包
  async importWallet(options: WalletImportOptions): Promise<WalletInfo> {
    try {
      // 验证私钥格式
      if (!options.privateKey || options.privateKey.length !== 64) {
        throw new Error('私钥格式无效，必须是64位十六进制字符串');
      }

      // 从私钥生成地址
      const address = this.tronWeb.address.fromPrivateKey(options.privateKey);
      
      // 验证地址是否有效
      if (!this.tronWeb.isAddress(address)) {
        throw new Error('生成的地址无效');
      }

      return {
        address: address,
        name: options.name,
        network: options.network || this.network
      };
    } catch (error: any) {
      throw new Error(`导入钱包失败: ${error.message}`);
    }
  }

  // 验证TRON地址
  isValidAddress(address: string): boolean {
    return this.tronWeb.isAddress(address);
  }

  // 获取钱包余额
  async getWalletBalance(address: string): Promise<WalletBalance> {
    try {
      console.log(`开始获取钱包余额 - 地址: ${address}, 网络: ${this.network}`);

      if (!this.isValidAddress(address)) {
        throw new Error('无效的TRON地址');
      }

      // 获取TRX余额
      console.log('获取TRX余额...');
      const trxBalance = await this.tronWeb.trx.getBalance(address);
      const trxAmount = this.tronWeb.fromSun(trxBalance);
      console.log(`TRX余额: ${trxAmount}`);

      // 获取USDT余额
      let usdtAmount = 0;
      try {
        console.log('获取USDT余额...');
        const usdtContract = USDT_CONTRACTS[this.network];
        console.log(`USDT合约地址: ${usdtContract}`);

        const contract = await this.tronWeb.contract().at(usdtContract);
        const usdtBalance = await contract.balanceOf(address).call();

        // USDT有6位小数，正确处理大数
        if (usdtBalance && usdtBalance.toString) {
          const balanceStr = usdtBalance.toString();
          usdtAmount = parseFloat(balanceStr) / Math.pow(10, 6);
        }

        console.log(`USDT余额: ${usdtAmount}`);
      } catch (usdtError: any) {
        console.warn('获取USDT余额失败:', usdtError.message);
        // USDT余额获取失败不影响TRX余额显示
      }

      const result = {
        TRX: parseFloat(trxAmount),
        USDT: usdtAmount
      };

      console.log('余额获取完成:', result);
      return result;

    } catch (error: any) {
      console.error('获取余额失败:', error);
      throw new Error(`获取余额失败: ${error.message}`);
    }
  }

  // 转账功能
  async transfer(options: TransferOptions): Promise<TransferResult> {
    try {
      if (!this.isValidAddress(options.fromAddress)) {
        throw new Error('发送地址无效');
      }
      if (!this.isValidAddress(options.toAddress)) {
        throw new Error('接收地址无效');
      }
      if (options.amount <= 0) {
        throw new Error('转账金额必须大于0');
      }

      // 设置私钥用于签名
      if (options.privateKey) {
        this.tronWeb.setPrivateKey(options.privateKey);
      }

      let txHash: string;

      if (options.currency === 'TRX') {
        // TRX转账
        const transaction = await this.tronWeb.transactionBuilder.sendTrx(
          options.toAddress,
          this.tronWeb.toSun(options.amount),
          options.fromAddress
        );
        
        const signedTx = await this.tronWeb.trx.sign(transaction);
        const result = await this.tronWeb.trx.sendRawTransaction(signedTx);
        
        if (!result.result) {
          throw new Error(result.message || 'TRX转账失败');
        }
        
        txHash = result.txid;
      } else if (options.currency === 'USDT') {
        // USDT转账
        const usdtContract = USDT_CONTRACTS[this.network];
        const contract = await this.tronWeb.contract().at(usdtContract);
        
        // USDT有6位小数
        const amount = Math.floor(options.amount * 1000000);
        
        const transaction = await contract.transfer(
          options.toAddress,
          amount
        ).send({
          from: options.fromAddress
        });
        
        txHash = transaction;
      } else {
        throw new Error('不支持的币种');
      }

      return {
        success: true,
        txHash: txHash,
        amount: options.amount,
        currency: options.currency,
        fromAddress: options.fromAddress,
        toAddress: options.toAddress
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '转账失败',
        amount: options.amount,
        currency: options.currency,
        fromAddress: options.fromAddress,
        toAddress: options.toAddress
      };
    }
  }

  // USDT转账（支持密码验证）
  async transferUSDT(fromAddress: string, password: string, toAddress: string, amount: number): Promise<TransferResult> {
    try {
      if (!this.isValidAddress(fromAddress)) {
        throw new Error('发送地址无效');
      }
      if (!this.isValidAddress(toAddress)) {
        throw new Error('接收地址无效');
      }
      if (amount <= 0) {
        throw new Error('转账金额必须大于0');
      }
      if (!password) {
        throw new Error('钱包密码不能为空');
      }

      // 获取USDT合约地址
      const usdtContract = USDT_CONTRACTS[this.network];
      if (!usdtContract) {
        throw new Error('当前网络不支持USDT转账');
      }

      // 注意：这个函数目前无法获取私钥，因为它没有数据库访问权限
      // 在实际应用中，应该：
      // 1. 从数据库获取加密的私钥
      // 2. 使用密码解密私钥
      // 3. 执行真实的转账

      // 由于架构限制，暂时返回错误，提示用户使用钱包页面的转账功能
      throw new Error('请使用钱包页面的转账功能进行USDT转账，该功能暂不支持直接转账');

      // 以下是真实转账的代码框架（需要私钥）
      /*
      // 从数据库获取并解密私钥
      const privateKey = await this.getDecryptedPrivateKey(fromAddress, password);

      // 设置私钥
      this.tronWeb.setPrivateKey(privateKey);

      // 获取合约实例
      const contract = await this.tronWeb.contract().at(usdtContract);

      // USDT有6位小数
      const amountInSun = Math.floor(amount * 1000000);

      // 执行转账
      const transaction = await contract.transfer(
        toAddress,
        amountInSun
      ).send({
        from: fromAddress
      });

      return {
        success: true,
        txHash: transaction,
        amount: amount,
        currency: 'USDT',
        fromAddress: fromAddress,
        toAddress: toAddress
      };
      */

      // 以下是真实的USDT转账代码（需要私钥）
      /*
      // 设置私钥（需要从安全存储中获取并解密）
      const privateKey = await this.getDecryptedPrivateKey(fromAddress, password);
      this.tronWeb.setPrivateKey(privateKey);

      const contract = await this.tronWeb.contract().at(usdtContract);

      // USDT有6位小数
      const amountInSun = Math.floor(amount * 1000000);

      const transaction = await contract.transfer(
        toAddress,
        amountInSun
      ).send({
        from: fromAddress
      });

      return {
        success: true,
        txHash: transaction,
        amount: amount,
        currency: 'USDT',
        fromAddress: fromAddress,
        toAddress: toAddress
      };
      */
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'USDT转账失败',
        amount: amount,
        currency: 'USDT',
        fromAddress: fromAddress,
        toAddress: toAddress
      };
    }
  }

  // 获取交易状态
  async getTransactionStatus(txHash: string): Promise<'pending' | 'confirmed' | 'failed'> {
    try {
      const transaction = await this.tronWeb.trx.getTransaction(txHash);

      if (!transaction) {
        return 'pending';
      }

      if (transaction.ret && transaction.ret[0] && transaction.ret[0].contractRet === 'SUCCESS') {
        return 'confirmed';
      } else {
        return 'failed';
      }
    } catch (error) {
      return 'pending';
    }
  }

  // 等待交易确认
  async waitForConfirmation(txHash: string, maxWaitTime: number = 60000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getTransactionStatus(txHash);
      
      if (status === 'confirmed') {
        return true;
      } else if (status === 'failed') {
        return false;
      }
      
      // 等待3秒后重试
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    return false; // 超时
  }

  // 生成收款二维码数据
  generateReceiveQRData(address: string, amount?: number, currency?: 'TRX' | 'USDT'): string {
    let qrData = `tron:${address}`;
    
    if (amount && currency) {
      qrData += `?amount=${amount}&token=${currency}`;
    }
    
    return qrData;
  }
}

// 创建钱包服务实例
export function createWalletService(network: 'mainnet' | 'nile' | 'shasta' = 'mainnet'): WalletService {
  return new WalletService(network);
}

// 密钥加密/解密工具（简单实现，生产环境需要更安全的方案）
export class KeyEncryption {
  static encrypt(privateKey: string, password: string): string {
    // 这里应该使用更安全的加密算法，如AES
    // 这只是一个示例实现
    const encrypted = Buffer.from(privateKey + '|' + password).toString('base64');
    return encrypted;
  }

  static decrypt(encryptedKey: string, password: string): string {
    try {
      const decrypted = Buffer.from(encryptedKey, 'base64').toString();
      const [privateKey, storedPassword] = decrypted.split('|');
      
      if (storedPassword !== password) {
        throw new Error('密码错误');
      }
      
      return privateKey;
    } catch (error) {
      throw new Error('解密失败');
    }
  }
}
