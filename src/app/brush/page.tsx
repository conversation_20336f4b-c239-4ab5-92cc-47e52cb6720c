'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/SessionProvider';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Spacer,
  Spinner,
  <PERSON>dal,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ooter,
  Input,
  Tabs,
  Tab,
  Divider,
  Progress
} from "@heroui/react";
import QRCodeComponent from '@/components/QRCode';
import BrushOrderList from '@/components/BrushOrderList';
import { createWalletService, type WalletBalance } from '@/lib/wallet-service';

interface Product {
  id: number;
  name: string;
  image?: string;
  price: number; // 已转换为元
  description?: string;
  status: number;
}

interface UserBalance {
  balance: number;
  frozenBalance: number;
  totalBalance: number;
  totalRecharged: number;
  totalSpent: number;
  stats: {
    totalOrders: number;
    completedOrders: number;
    burstOrders: number;
    totalCommission: number;
    todayOrders: number;
    todayCommission: number;
    lastOrderAt?: string;
  };
}

interface RechargeInfo {
  platformAddress: string;
  network: string;
  currency: string;
  exchangeRate: number;
  minRecharge: number;
  description: string;
}

interface UserWallet {
  id: number;
  name: string;
  address: string;
  wallet_type: 'created' | 'imported';
  network?: 'mainnet' | 'nile' | 'shasta';
  is_default: boolean;
  balance?: WalletBalance;
}

export default function BrushPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<Product[]>([]);
  const [userBalance, setUserBalance] = useState<UserBalance | null>(null);
  const [rechargeInfo, setRechargeInfo] = useState<RechargeInfo | null>(null);
  const [selectedTab, setSelectedTab] = useState('products');

  // 钱包相关状态
  const [userWallets, setUserWallets] = useState<UserWallet[]>([]);
  const [selectedWallet, setSelectedWallet] = useState<UserWallet | null>(null);

  // 模态框状态
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showAutoRechargeModal, setShowAutoRechargeModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  
  // 充值表单
  const [rechargeForm, setRechargeForm] = useState({
    txHash: '',
    amount: '',
    fromAddress: ''
  });

  // 自动充值表单
  const [autoRechargeForm, setAutoRechargeForm] = useState({
    amount: '',
    password: ''
  });

  // 下单状态
  const [ordering, setOrdering] = useState(false);
  const [recharging, setRecharging] = useState(false);
  const [autoRecharging, setAutoRecharging] = useState(false);

  // 检查登录状态
  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/auth/signin');
      return;
    }

    loadInitialData();
  }, [user, authLoading]);

  // 监听数据变化，检查是否需要显示充值弹窗
  useEffect(() => {
    if (loading || !userBalance || !rechargeInfo) return;

    // 延迟检查，确保所有数据都已加载
    const timer = setTimeout(() => {
      checkAutoRechargeModal();
    }, 1000);

    return () => clearTimeout(timer);
  }, [userBalance, userWallets, rechargeInfo, loading]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadProducts(),
        loadUserBalance(),
        loadRechargeInfo(),
        loadUserWallets()
      ]);

      // 数据加载完成，useEffect 会自动检查充值弹窗
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await fetch('/api/products');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 将价格从分转换为元
          const formattedProducts = result.data.map((product: any) => ({
            ...product,
            price: product.price / 100
          }));
          setProducts(formattedProducts);
        }
      }
    } catch (error) {
      console.error('加载商品失败:', error);
    }
  };

  const loadUserBalance = async () => {
    try {
      const response = await fetch('/api/brush/balance');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setUserBalance(result.data);
        }
      }
    } catch (error) {
      console.error('加载用户余额失败:', error);
    }
  };

  const loadRechargeInfo = async () => {
    try {
      const response = await fetch('/api/brush/recharge?network=mainnet');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRechargeInfo(result.data);
        }
      }
    } catch (error) {
      console.error('加载充值信息失败:', error);
    }
  };

  const loadUserWallets = async () => {
    try {
      const response = await fetch('/api/wallets');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          const walletsWithBalance = await Promise.all(
            result.data.map(async (wallet: any) => {
              try {
                const walletService = createWalletService(wallet.network || 'mainnet');
                const balance = await walletService.getWalletBalance(wallet.address);
                return { ...wallet, balance };
              } catch (error) {
                console.error(`获取钱包 ${wallet.address} 余额失败:`, error);
                return wallet;
              }
            })
          );
          setUserWallets(walletsWithBalance);
          console.log('💰 钱包余额加载完成:', walletsWithBalance);

          // 设置默认钱包
          const defaultWallet = walletsWithBalance.find((w: any) => w.is_default) || walletsWithBalance[0];
          if (defaultWallet) {
            setSelectedWallet(defaultWallet);
          }
        }
      }
    } catch (error) {
      console.error('加载用户钱包失败:', error);
    }
  };

  const checkAutoRechargeModal = async () => {
    console.log('🔍 检查自动充值弹窗条件:');
    console.log('- userBalance:', userBalance);
    console.log('- userWallets:', userWallets);
    console.log('- rechargeInfo:', rechargeInfo);

    // 检查用户是否有内部余额，如果没有或余额很少，智能提示充值
    if (userBalance && userBalance.balance < 10) { // 余额少于10元时提示充值
      console.log('✅ 余额不足，需要充值');

      if (userWallets.length > 0 && rechargeInfo) {
        console.log('� 显示充值弹窗');
        // 有钱包就显示充值弹窗
        setTimeout(() => {
          // 选择第一个钱包作为默认钱包
          setSelectedWallet(userWallets[0]);
          setShowRechargeModal(true);
        }, 1500);
      } else if (userWallets.length === 0) {
        console.log('👛 提示创建钱包');
        // 如果没有钱包，提示创建钱包
        setTimeout(() => {
          if (confirm('检测到您还没有钱包，是否前往创建钱包？')) {
            router.push('/wallet');
          }
        }, 1500);
      } else {
        console.log('⏳ 等待充值信息加载');
      }
    } else {
      console.log('✅ 余额充足，无需充值');
    }
  };

  const handleRecharge = async () => {
    if (!rechargeForm.txHash || !rechargeForm.amount || !rechargeForm.fromAddress) {
      alert('请填写完整的充值信息');
      return;
    }

    const amount = parseFloat(rechargeForm.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('请输入有效的充值金额');
      return;
    }

    if (rechargeInfo && amount < rechargeInfo.minRecharge) {
      alert(`充值金额不能少于 ${rechargeInfo.minRecharge} USDT`);
      return;
    }

    try {
      setRecharging(true);
      const response = await fetch('/api/brush/recharge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          txHash: rechargeForm.txHash,
          amount: amount,
          fromAddress: rechargeForm.fromAddress,
          network: 'mainnet'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert('充值记录已提交，请等待系统确认');
          setShowRechargeModal(false);
          setRechargeForm({ txHash: '', amount: '', fromAddress: '' });
          await loadUserBalance();
        } else {
          alert(`充值失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`充值失败: ${error.error}`);
      }
    } catch (error) {
      console.error('充值失败:', error);
      alert('充值失败，请重试');
    } finally {
      setRecharging(false);
    }
  };

  // 一键快速充值
  const handleQuickRecharge = async (amount: number = 10) => {
    if (!selectedWallet || !rechargeInfo) {
      alert('请先选择钱包');
      return;
    }

    // 检查钱包USDT余额
    if (!selectedWallet.balance || selectedWallet.balance.USDT < amount) {
      alert(`钱包USDT余额不足，需要至少 ${amount} USDT`);
      return;
    }

    // 提示用户输入密码
    const password = prompt('请输入钱包密码以确认充值：');
    if (!password) {
      return;
    }

    try {
      setAutoRecharging(true);

      // 创建钱包服务
      const walletService = createWalletService(selectedWallet.network || 'mainnet');

      // 执行USDT转账
      const transferResult = await walletService.transferUSDT(
        selectedWallet.address,
        password,
        rechargeInfo.platformAddress,
        amount
      );

      if (transferResult.success && transferResult.txHash) {
        // 自动提交充值记录
        const rechargeResponse = await fetch('/api/brush/recharge', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            txHash: transferResult.txHash,
            amount: amount,
            fromAddress: selectedWallet.address,
            network: selectedWallet.network || 'mainnet'
          }),
        });

        if (rechargeResponse.ok) {
          const result = await rechargeResponse.json();
          if (result.success) {
            alert(`🎉 充值成功！已转账 ${amount} USDT，系统将在确认后为您充值到内部账户`);
            setShowAutoRechargeModal(false);
            setAutoRechargeForm({ amount: '', password: '' });
            await loadUserBalance();
            await loadUserWallets(); // 刷新钱包余额
          } else {
            alert(`充值记录提交失败: ${result.error}`);
          }
        } else {
          alert('充值记录提交失败，但转账已成功，请联系客服处理');
        }
      } else {
        alert(`转账失败: ${transferResult.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('快速充值失败:', error);
      alert('快速充值失败，请重试');
    } finally {
      setAutoRecharging(false);
    }
  };

  const handleAutoRecharge = async () => {
    if (!selectedWallet || !rechargeInfo) {
      alert('请先选择钱包');
      return;
    }

    if (!autoRechargeForm.amount || !autoRechargeForm.password) {
      alert('请填写充值金额和钱包密码');
      return;
    }

    const amount = parseFloat(autoRechargeForm.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('请输入有效的充值金额');
      return;
    }

    if (amount < rechargeInfo.minRecharge) {
      alert(`充值金额不能少于 ${rechargeInfo.minRecharge} USDT`);
      return;
    }

    // 检查钱包USDT余额
    if (!selectedWallet.balance || selectedWallet.balance.USDT < amount) {
      alert('钱包USDT余额不足');
      return;
    }

    try {
      setAutoRecharging(true);

      // 创建钱包服务
      const walletService = createWalletService(selectedWallet.network || 'mainnet');

      // 执行USDT转账
      const transferResult = await walletService.transferUSDT(
        selectedWallet.address,
        autoRechargeForm.password,
        rechargeInfo.platformAddress,
        amount
      );

      if (transferResult.success && transferResult.txHash) {
        // 自动提交充值记录
        const rechargeResponse = await fetch('/api/brush/recharge', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            txHash: transferResult.txHash,
            amount: amount,
            fromAddress: selectedWallet.address,
            network: selectedWallet.network || 'mainnet'
          }),
        });

        if (rechargeResponse.ok) {
          const result = await rechargeResponse.json();
          if (result.success) {
            alert('自动充值成功！系统将在确认后为您充值到内部账户');
            setShowAutoRechargeModal(false);
            setAutoRechargeForm({ amount: '', password: '' });
            await loadUserBalance();
            await loadUserWallets(); // 刷新钱包余额
          } else {
            alert(`充值记录提交失败: ${result.error}`);
          }
        } else {
          alert('充值记录提交失败，但转账已成功，请手动提交交易哈希');
        }
      } else {
        alert(`转账失败: ${transferResult.error || '未知错误'}`);
      }
    } catch (error) {
      console.error('自动充值失败:', error);
      alert('自动充值失败，请重试');
    } finally {
      setAutoRecharging(false);
    }
  };

  const handleOrder = async () => {
    if (!selectedProduct) return;

    if (!userBalance || userBalance.balance < selectedProduct.price) {
      alert('余额不足，请先充值');
      return;
    }

    try {
      setOrdering(true);
      const response = await fetch('/api/brush/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId: selectedProduct.id,
          quantity: 1
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          alert(result.message);
          setShowOrderModal(false);
          setSelectedProduct(null);
          await loadUserBalance();
        } else {
          alert(`下单失败: ${result.error}`);
        }
      } else {
        const error = await response.json();
        alert(`下单失败: ${error.error}`);
      }
    } catch (error) {
      console.error('下单失败:', error);
      alert('下单失败，请重试');
    } finally {
      setOrdering(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🎯 刷单赚钱</h1>
            <p className="text-gray-600 mt-1">轻松刷单，稳定收益</p>
          </div>
          <div className="flex gap-3">
            <Button
              color="warning"
              size="sm"
              onPress={() => {
                console.log('强制显示充值弹窗');
                setShowAutoRechargeModal(true);
              }}
            >
              测试弹窗
            </Button>
            <Button
              color="primary"
              variant="bordered"
              onPress={() => router.push('/wallet')}
            >
              返回钱包
            </Button>
          </div>
        </div>

        {/* 余额卡片 */}
        {userBalance && (
          <Card className="mb-6">
            <CardHeader>
              <h3 className="text-lg font-semibold">账户余额</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    ¥{userBalance.balance.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-500">可用余额</p>
                </div>
                <div className="text-center">
                  <p className="text-xl font-semibold text-blue-600">
                    {userBalance.stats.todayOrders}
                  </p>
                  <p className="text-sm text-gray-500">今日订单</p>
                </div>
                <div className="text-center">
                  <p className="text-xl font-semibold text-purple-600">
                    ¥{userBalance.stats.todayCommission.toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-500">今日佣金</p>
                </div>
                <div className="text-center">
                  <p className="text-xl font-semibold text-orange-600">
                    {userBalance.stats.totalOrders}
                  </p>
                  <p className="text-sm text-gray-500">总订单数</p>
                </div>
              </div>
              <Spacer y={4} />

              {/* 余额不足提示 */}
              {userBalance.balance < 10 && (
                <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-2xl mr-3">⚠️</span>
                      <div>
                        <p className="font-semibold text-orange-800">余额不足</p>
                        <p className="text-sm text-orange-700">建议充值后再进行刷单操作</p>
                      </div>
                    </div>
                    {userWallets.find(w => w.balance && w.balance.USDT >= 10) && (
                      <Button
                        color="warning"
                        size="sm"
                        onPress={() => {
                          const availableWallet = userWallets.find(w => w.balance && w.balance.USDT >= 10);
                          if (availableWallet) {
                            setSelectedWallet(availableWallet);
                            setAutoRechargeForm(prev => ({ ...prev, amount: '10' }));
                            setShowAutoRechargeModal(true);
                          }
                        }}
                      >
                        🚀 快速充值
                      </Button>
                    )}
                  </div>
                </div>
              )}

              <div className="flex gap-4 justify-center">
                <Button
                  color="success"
                  size="lg"
                  onPress={() => setShowRechargeModal(true)}
                >
                  手动充值
                </Button>
                {userWallets.find(w => w.balance && w.balance.USDT >= 10) && (
                  <Button
                    color="warning"
                    size="lg"
                    onPress={() => {
                      const availableWallet = userWallets.find(w => w.balance && w.balance.USDT >= 10);
                      if (availableWallet) {
                        setSelectedWallet(availableWallet);
                        setAutoRechargeForm(prev => ({ ...prev, amount: '10' }));
                        setShowAutoRechargeModal(true);
                      }
                    }}
                  >
                    ⚡ 智能充值
                  </Button>
                )}
                <Button
                  color="primary"
                  variant="bordered"
                  size="lg"
                  onPress={() => setSelectedTab('orders')}
                >
                  订单记录
                </Button>
              </div>
            </CardBody>
          </Card>
        )}

        {/* 主要内容 */}
        <Tabs
          selectedKey={selectedTab}
          onSelectionChange={(key) => setSelectedTab(key as string)}
          className="w-full"
        >
          <Tab key="products" title="商品列表">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
              {products.map((product) => (
                <Card key={product.id} className="hover:shadow-lg transition-shadow">
                  <CardBody>
                    {product.image && (
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-48 object-cover rounded-lg mb-4"
                      />
                    )}
                    <h4 className="text-lg font-semibold mb-2">{product.name}</h4>
                    <p className="text-gray-600 text-sm mb-4">{product.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-2xl font-bold text-red-600">
                        ¥{product.price.toFixed(2)}
                      </span>
                      <Button
                        color="primary"
                        size="sm"
                        onPress={() => {
                          setSelectedProduct(product);
                          setShowOrderModal(true);
                        }}
                        disabled={!userBalance || userBalance.balance < product.price}
                      >
                        {!userBalance || userBalance.balance < product.price ? '余额不足' : '立即下单'}
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </Tab>
          
          <Tab key="orders" title="订单记录">
            <div className="mt-6">
              <BrushOrderList />
            </div>
          </Tab>
        </Tabs>

        {/* 充值模态框 */}
        <Modal
          isOpen={showRechargeModal}
          onClose={() => setShowRechargeModal(false)}
          size="lg"
        >
          <ModalContent>
            <ModalHeader>
              <h3 className="text-xl font-semibold">USDT充值</h3>
            </ModalHeader>
            <ModalBody>
              {rechargeInfo && (
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-800 mb-2">充值说明：</p>
                    <p className="text-sm text-blue-700">{rechargeInfo.description}</p>
                  </div>

                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">平台收款地址：</p>
                    <div className="bg-gray-100 p-3 rounded-lg">
                      <p className="font-mono text-sm break-all">{rechargeInfo.platformAddress}</p>
                    </div>
                    <div className="mt-4">
                      <QRCodeComponent value={rechargeInfo.platformAddress} size={200} />
                    </div>
                  </div>

                  <Divider />

                  <div className="space-y-4">
                    <Input
                      label="交易哈希"
                      placeholder="请输入USDT转账的交易哈希"
                      value={rechargeForm.txHash}
                      onChange={(e) => setRechargeForm(prev => ({ ...prev, txHash: e.target.value }))}
                    />
                    <Input
                      label="充值金额"
                      placeholder={`最小充值金额: ${rechargeInfo.minRecharge} USDT`}
                      type="number"
                      value={rechargeForm.amount}
                      onChange={(e) => setRechargeForm(prev => ({ ...prev, amount: e.target.value }))}
                      endContent={<span className="text-gray-500">USDT</span>}
                    />
                    <Input
                      label="发送地址"
                      placeholder="请输入您的钱包地址"
                      value={rechargeForm.fromAddress}
                      onChange={(e) => setRechargeForm(prev => ({ ...prev, fromAddress: e.target.value }))}
                    />
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                variant="light"
                onPress={() => setShowRechargeModal(false)}
              >
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleRecharge}
                isLoading={recharging}
              >
                提交充值
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 下单确认模态框 */}
        <Modal
          isOpen={showOrderModal}
          onClose={() => setShowOrderModal(false)}
          size="md"
        >
          <ModalContent>
            <ModalHeader>
              <h3 className="text-xl font-semibold">确认下单</h3>
            </ModalHeader>
            <ModalBody>
              {selectedProduct && (
                <div className="space-y-4">
                  <div className="text-center">
                    {selectedProduct.image && (
                      <img
                        src={selectedProduct.image}
                        alt={selectedProduct.name}
                        className="w-32 h-32 object-cover rounded-lg mx-auto mb-4"
                      />
                    )}
                    <h4 className="text-lg font-semibold">{selectedProduct.name}</h4>
                    <p className="text-gray-600 text-sm">{selectedProduct.description}</p>
                  </div>

                  <Divider />

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>商品价格：</span>
                      <span className="font-semibold">¥{selectedProduct.price.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>购买数量：</span>
                      <span className="font-semibold">1</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold">
                      <span>总计：</span>
                      <span className="text-red-600">¥{selectedProduct.price.toFixed(2)}</span>
                    </div>
                  </div>

                  {userBalance && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex justify-between text-sm">
                        <span>当前余额：</span>
                        <span>¥{userBalance.balance.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>下单后余额：</span>
                        <span>¥{(userBalance.balance - selectedProduct.price).toFixed(2)}</span>
                      </div>
                    </div>
                  )}

                  <div className="bg-yellow-50 p-3 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      ⚠️ 温馨提示：下单后系统将随机分配佣金，完成订单可获得本金+佣金返还。
                      部分订单可能会爆单，请合理安排资金。
                    </p>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button
                color="danger"
                variant="light"
                onPress={() => setShowOrderModal(false)}
              >
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleOrder}
                isLoading={ordering}
              >
                确认下单
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 自动充值模态框 */}
        <Modal
          isOpen={showAutoRechargeModal}
          onClose={() => setShowAutoRechargeModal(false)}
          size="lg"
          isDismissable={false}
        >
          <ModalContent>
            <ModalHeader>
              <h3 className="text-xl font-semibold">🚀 智能充值</h3>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-center mb-2">
                    <span className="text-lg mr-2">⚡</span>
                    <p className="text-sm font-medium text-blue-800">自动检测到余额不足</p>
                  </div>
                  <p className="text-sm text-blue-700">
                    当前内部余额：<span className="font-semibold">{userBalance?.balance?.toFixed(2) || '0.00'}</span> 元
                  </p>
                  <p className="text-sm text-blue-700">
                    系统已为您选择最佳钱包进行快速充值，只需输入密码即可完成。
                  </p>
                </div>

                {/* 钱包选择 */}
                {userWallets.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">选择钱包：</label>
                    <div className="grid gap-2">
                      {userWallets.map((wallet) => (
                        <div
                          key={wallet.id}
                          className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                            selectedWallet?.id === wallet.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedWallet(wallet)}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium">{wallet.name}</p>
                              <p className="text-sm text-gray-500">
                                {wallet.address.slice(0, 8)}...{wallet.address.slice(-6)}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium">
                                {wallet.balance?.USDT?.toFixed(2) || '0.00'} USDT
                              </p>
                              <p className="text-xs text-gray-500">
                                {wallet.balance?.TRX?.toFixed(2) || '0.00'} TRX
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedWallet && rechargeInfo && (
                  <div className="space-y-4">
                    <Divider />

                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex justify-between text-sm">
                        <span>平台地址：</span>
                        <span className="font-mono text-xs">
                          {rechargeInfo.platformAddress.slice(0, 10)}...{rechargeInfo.platformAddress.slice(-8)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>汇率：</span>
                        <span>1 USDT = {rechargeInfo.exchangeRate} 内部币</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>最小充值：</span>
                        <span>{rechargeInfo.minRecharge} USDT</span>
                      </div>
                    </div>

                    <Input
                      label="充值金额"
                      placeholder={`最小充值: ${rechargeInfo.minRecharge} USDT`}
                      type="number"
                      value={autoRechargeForm.amount}
                      onChange={(e) => setAutoRechargeForm(prev => ({ ...prev, amount: e.target.value }))}
                      endContent={<span className="text-gray-500">USDT</span>}
                      description={`钱包余额: ${selectedWallet.balance?.USDT?.toFixed(2) || '0.00'} USDT`}
                    />

                    <Input
                      label="钱包密码"
                      placeholder="请输入钱包密码以确认转账"
                      type="password"
                      value={autoRechargeForm.password}
                      onChange={(e) => setAutoRechargeForm(prev => ({ ...prev, password: e.target.value }))}
                    />

                    {autoRechargeForm.amount && (
                      <div className="bg-green-50 p-3 rounded-lg">
                        <p className="text-sm text-green-800">
                          充值 {autoRechargeForm.amount} USDT 将获得 {(parseFloat(autoRechargeForm.amount || '0') * rechargeInfo.exchangeRate).toFixed(2)} 内部币
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </ModalBody>
            <ModalFooter>
              <div className="flex flex-col w-full gap-3">
                {/* 一键充值按钮 */}
                {selectedWallet && selectedWallet.balance && selectedWallet.balance.USDT >= 10 && (
                  <Button
                    color="success"
                    variant="solid"
                    onPress={() => handleQuickRecharge(10)}
                    className="w-full font-semibold"
                    size="lg"
                    isLoading={autoRecharging}
                    isDisabled={autoRecharging}
                  >
                    🚀 一键充值 10 USDT (推荐)
                  </Button>
                )}

                <div className="flex gap-2">
                  <Button
                    color="danger"
                    variant="light"
                    onPress={() => setShowAutoRechargeModal(false)}
                    className="flex-1"
                  >
                    稍后充值
                  </Button>
                  <Button
                    color="primary"
                    onPress={handleAutoRecharge}
                    isLoading={autoRecharging}
                    isDisabled={!selectedWallet || !autoRechargeForm.amount || !autoRechargeForm.password}
                    className="flex-1"
                  >
                    {autoRecharging ? '充值中...' : '立即充值'}
                  </Button>
                </div>
              </div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
