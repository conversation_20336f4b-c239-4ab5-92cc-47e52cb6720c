import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取用户列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;

    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ error: 'Token无效' }, { status: 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT
        u.id, u.username,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ error: '无管理员权限' }, { status: 403 });
    }

    let usersQuery = '';
    let queryParams: any[] = [];

    if (adminResult.role === 'super_admin') {
      // 超级管理员可以查看所有用户
      usersQuery = `
        SELECT
          u.id, u.username, u.email, u.name, u.is_active, u.created_at,
          a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active,
          ic.code as invite_code,
          inviter.username as invited_by_username
        FROM users u
        LEFT JOIN admins a ON u.id = a.user_id
        LEFT JOIN invite_codes ic ON u.invite_code_id = ic.id
        LEFT JOIN users inviter ON u.invited_by = inviter.id
        ORDER BY u.created_at DESC
      `;
    } else {
      // 代理只能查看自己邀请的用户
      usersQuery = `
        SELECT
          u.id, u.username, u.email, u.name, u.is_active, u.created_at,
          a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active,
          ic.code as invite_code,
          inviter.username as invited_by_username
        FROM users u
        LEFT JOIN admins a ON u.id = a.user_id
        LEFT JOIN invite_codes ic ON u.invite_code_id = ic.id
        LEFT JOIN users inviter ON u.invited_by = inviter.id
        WHERE u.invited_by = ? OR u.invited_by IN (
          SELECT u2.id FROM users u2
          JOIN admins a2 ON u2.id = a2.user_id
          WHERE a2.parent_admin_id = ?
        )
        ORDER BY u.created_at DESC
      `;
      queryParams = [decoded.userId, adminResult.id];
    }

    const users = await db.prepare(usersQuery).bind(...queryParams).all();

    // 处理用户数据
    const processedUsers = users.results?.map((user: any) => {
      let permissions: string[] = [];
      if (user.permissions) {
        try {
          permissions = JSON.parse(user.permissions);
        } catch (error) {
          console.error('解析权限失败:', error);
        }
      }

      return {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role || 'user',
        permissions: permissions,
        parent_admin_id: user.parent_admin_id,
        is_active: user.is_active,
        admin_active: user.admin_active,
        invite_code: user.invite_code,
        invited_by_username: user.invited_by_username,
        created_at: user.created_at
      };
    }) || [];

    return NextResponse.json(processedUsers);

  } catch (error: any) {
    console.error('获取用户列表失败:', error);
    return NextResponse.json({
      error: error.message || '获取用户列表失败'
    }, { status: 500 });
  }
}