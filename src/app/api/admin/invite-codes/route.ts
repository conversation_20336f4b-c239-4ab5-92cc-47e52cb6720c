import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取邀请码列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    const token = request.cookies.get('auth-token')?.value;
    if (!token) {
      return NextResponse.json({ error: '未登录' }, { status: 401 });
    }

    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    let decoded: any;
    
    try {
      decoded = jwt.verify(token, jwtSecret);
    } catch (error) {
      return NextResponse.json({ error: 'Token无效' }, { status: 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 获取当前管理员信息
    const adminResult = await db.prepare(`
      SELECT 
        u.id, u.username,
        a.role, a.permissions, a.parent_admin_id, a.is_active as admin_active
      FROM users u
      JOIN admins a ON u.id = a.user_id
      WHERE u.id = ? AND u.is_active = TRUE AND a.is_active = TRUE
    `).bind(decoded.userId).first();

    if (!adminResult) {
      return NextResponse.json({ error: '无管理员权限' }, { status: 403 });
    }

    let inviteCodesQuery = '';
    let queryParams: any[] = [];

    if (adminResult.role === 'super_admin') {
      // 超级管理员可以查看所有邀请码
      inviteCodesQuery = `
        SELECT 
          ic.id, ic.code, ic.created_by, ic.used_by, ic.is_active,
          ic.max_uses, ic.used_count, ic.expires_at, ic.created_at, ic.used_at,
          creator.username as created_by_username,
          user.username as used_by_username
        FROM invite_codes ic
        LEFT JOIN users creator ON ic.created_by = creator.id
        LEFT JOIN users user ON ic.used_by = user.id
        ORDER BY ic.created_at DESC
      `;
    } else {
      // 代理只能查看自己创建的邀请码
      inviteCodesQuery = `
        SELECT 
          ic.id, ic.code, ic.created_by, ic.used_by, ic.is_active,
          ic.max_uses, ic.used_count, ic.expires_at, ic.created_at, ic.used_at,
          creator.username as created_by_username,
          user.username as used_by_username
        FROM invite_codes ic
        LEFT JOIN users creator ON ic.created_by = creator.id
        LEFT JOIN users user ON ic.used_by = user.id
        WHERE ic.created_by = ?
        ORDER BY ic.created_at DESC
      `;
      queryParams = [decoded.userId];
    }

    const inviteCodes = await db.prepare(inviteCodesQuery).bind(...queryParams).all();

    // 处理邀请码数据
    const processedInviteCodes = inviteCodes.results?.map((code: any) => ({
      id: code.id,
      code: code.code,
      created_by: code.created_by,
      used_by: code.used_by,
      is_active: code.is_active,
      max_uses: code.max_uses,
      used_count: code.used_count,
      expires_at: code.expires_at,
      created_at: code.created_at,
      used_at: code.used_at,
      created_by_username: code.created_by_username,
      used_by_username: code.used_by_username
    })) || [];

    return NextResponse.json({
      success: true,
      data: processedInviteCodes
    });

  } catch (error: any) {
    console.error('获取邀请码列表失败:', error);
    return NextResponse.json({ 
      error: error.message || '获取邀请码列表失败' 
    }, { status: 500 });
  }
}
