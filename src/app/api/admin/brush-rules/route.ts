import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取刷单规则列表
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    const rules = await dbService.getAllBrushRules();

    return NextResponse.json({
      success: true,
      data: rules
    });
  } catch (error) {
    console.error('获取刷单规则失败:', error);
    return NextResponse.json({ error: '获取刷单规则失败' }, { status: 500 });
  }
}

// 创建刷单规则
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      description,
      burstProbability = 0.1,
      burstOrderRange = '3-7',
      minCommissionRate = 0.03,
      maxCommissionRate = 0.08,
      dailyOrderLimit = 50,
      minOrderInterval = 30,
      maxOrderInterval = 300,
      configJson
    } = body;

    if (!name) {
      return NextResponse.json({ error: '规则名称不能为空' }, { status: 400 });
    }

    // 验证参数范围
    if (burstProbability < 0 || burstProbability > 1) {
      return NextResponse.json({ error: '爆单概率必须在0-1之间' }, { status: 400 });
    }

    if (minCommissionRate < 0 || maxCommissionRate > 1 || minCommissionRate >= maxCommissionRate) {
      return NextResponse.json({ error: '佣金比例设置无效' }, { status: 400 });
    }

    if (dailyOrderLimit <= 0) {
      return NextResponse.json({ error: '每日订单限制必须大于0' }, { status: 400 });
    }

    if (minOrderInterval <= 0 || maxOrderInterval <= 0 || minOrderInterval >= maxOrderInterval) {
      return NextResponse.json({ error: '下单间隔设置无效' }, { status: 400 });
    }

    // 验证爆单订单范围格式
    const rangePattern = /^\d+-\d+$/;
    if (!rangePattern.test(burstOrderRange)) {
      return NextResponse.json({ error: '爆单订单范围格式无效，应为 "数字-数字"' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    try {
      const rule = await dbService.createBrushRule({
        name: name.trim(),
        description: description?.trim(),
        burstProbability,
        burstOrderRange,
        minCommissionRate,
        maxCommissionRate,
        dailyOrderLimit,
        minOrderInterval,
        maxOrderInterval,
        configJson: configJson ? JSON.stringify(configJson) : undefined,
        createdBy: authResult.user.id
      });

      return NextResponse.json({
        success: true,
        data: rule
      });
    } catch (error) {
      console.error('创建刷单规则失败:', error);
      return NextResponse.json({ error: '创建刷单规则失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('创建刷单规则失败:', error);
    return NextResponse.json({ error: '创建刷单规则失败' }, { status: 500 });
  }
}

// 更新刷单规则
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const {
      id,
      name,
      description,
      isActive,
      burstProbability,
      burstOrderRange,
      minCommissionRate,
      maxCommissionRate,
      dailyOrderLimit,
      minOrderInterval,
      maxOrderInterval,
      configJson
    } = body;

    if (!id) {
      return NextResponse.json({ error: '规则ID不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查规则是否存在
    const existingRule = await dbService.getBrushRuleById(id);
    if (!existingRule) {
      return NextResponse.json({ error: '刷单规则不存在' }, { status: 404 });
    }

    // 验证参数
    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description?.trim();
    if (isActive !== undefined) updateData.isActive = isActive;
    
    if (burstProbability !== undefined) {
      if (burstProbability < 0 || burstProbability > 1) {
        return NextResponse.json({ error: '爆单概率必须在0-1之间' }, { status: 400 });
      }
      updateData.burstProbability = burstProbability;
    }
    
    if (burstOrderRange !== undefined) {
      const rangePattern = /^\d+-\d+$/;
      if (!rangePattern.test(burstOrderRange)) {
        return NextResponse.json({ error: '爆单订单范围格式无效' }, { status: 400 });
      }
      updateData.burstOrderRange = burstOrderRange;
    }
    
    if (minCommissionRate !== undefined) {
      if (minCommissionRate < 0 || minCommissionRate > 1) {
        return NextResponse.json({ error: '最小佣金比例无效' }, { status: 400 });
      }
      updateData.minCommissionRate = minCommissionRate;
    }
    
    if (maxCommissionRate !== undefined) {
      if (maxCommissionRate < 0 || maxCommissionRate > 1) {
        return NextResponse.json({ error: '最大佣金比例无效' }, { status: 400 });
      }
      updateData.maxCommissionRate = maxCommissionRate;
    }
    
    if (dailyOrderLimit !== undefined) {
      if (dailyOrderLimit <= 0) {
        return NextResponse.json({ error: '每日订单限制必须大于0' }, { status: 400 });
      }
      updateData.dailyOrderLimit = dailyOrderLimit;
    }
    
    if (minOrderInterval !== undefined) {
      if (minOrderInterval <= 0) {
        return NextResponse.json({ error: '最小下单间隔必须大于0' }, { status: 400 });
      }
      updateData.minOrderInterval = minOrderInterval;
    }
    
    if (maxOrderInterval !== undefined) {
      if (maxOrderInterval <= 0) {
        return NextResponse.json({ error: '最大下单间隔必须大于0' }, { status: 400 });
      }
      updateData.maxOrderInterval = maxOrderInterval;
    }
    
    if (configJson !== undefined) {
      updateData.configJson = configJson ? JSON.stringify(configJson) : null;
    }

    // 验证佣金比例范围
    const finalMinRate = updateData.minCommissionRate ?? existingRule.min_commission_rate;
    const finalMaxRate = updateData.maxCommissionRate ?? existingRule.max_commission_rate;
    if (finalMinRate >= finalMaxRate) {
      return NextResponse.json({ error: '最小佣金比例必须小于最大佣金比例' }, { status: 400 });
    }

    // 验证下单间隔范围
    const finalMinInterval = updateData.minOrderInterval ?? existingRule.min_order_interval;
    const finalMaxInterval = updateData.maxOrderInterval ?? existingRule.max_order_interval;
    if (finalMinInterval >= finalMaxInterval) {
      return NextResponse.json({ error: '最小下单间隔必须小于最大下单间隔' }, { status: 400 });
    }

    try {
      const success = await dbService.updateBrushRule(id, updateData);

      if (!success) {
        return NextResponse.json({ error: '更新刷单规则失败' }, { status: 500 });
      }

      return NextResponse.json({
        success: true,
        message: '刷单规则更新成功'
      });
    } catch (error) {
      console.error('更新刷单规则失败:', error);
      return NextResponse.json({ error: '更新刷单规则失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('更新刷单规则失败:', error);
    return NextResponse.json({ error: '更新刷单规则失败' }, { status: 500 });
  }
}
