import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getAccountBalance } from '@/lib/tronweb-server';

// 获取钱包余额
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ 
        error: authResult.error || '未登录' 
      }, { status: authResult.status || 401 });
    }

    const body = await request.json();
    const { address, currency, network } = body as {
      address: string;
      currency: 'TRX' | 'USDT';
      network?: 'mainnet' | 'nile' | 'shasta';
    };

    if (!address?.trim()) {
      return NextResponse.json({ error: '钱包地址不能为空' }, { status: 400 });
    }

    if (!currency || !['TRX', 'USDT'].includes(currency)) {
      return NextResponse.json({ error: '无效的币种' }, { status: 400 });
    }

    try {
      const balance = await getAccountBalance(address, currency, network || 'mainnet');
      
      return NextResponse.json({
        success: true,
        balance: balance,
        currency: currency,
        address: address,
        network: network || 'mainnet'
      });
    } catch (error: any) {
      console.error('获取余额失败:', error);
      return NextResponse.json({ 
        error: `获取${currency}余额失败: ${error.message}` 
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('余额查询API失败:', error);
    return NextResponse.json({ 
      error: error.message || '余额查询失败' 
    }, { status: 500 });
  }
}
