import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取用户钱包列表
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 使用认证用户的ID
    const userId = authResult.user.id;

    // 获取用户的钱包列表
    const wallets = await db.prepare(`
      SELECT id, name, address, wallet_type, network, is_default, is_active, created_at
      FROM wallets
      WHERE user_id = ? AND is_active = TRUE
      ORDER BY is_default DESC, created_at DESC
    `).bind(userId).all();

    return NextResponse.json(wallets.results || []);
  } catch (error) {
    console.error('获取钱包列表失败:', error);
    return NextResponse.json({ error: '获取钱包列表失败' }, { status: 500 });
  }
}
