import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取商品列表
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return NextResponse.json({ error: '刷单功能未启用' }, { status: 403 });
    }

    // 普通用户只能看到上架的商品
    const products = await dbService.getActiveProducts();

    return NextResponse.json({
      success: true,
      data: products
    });
  } catch (error) {
    console.error('获取商品列表失败:', error);
    return NextResponse.json({ error: '获取商品列表失败' }, { status: 500 });
  }
}

// 创建商品（仅管理员）
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { name, image, price, description, status = 1 } = body;

    if (!name || !price) {
      return NextResponse.json({ error: '商品名称和价格不能为空' }, { status: 400 });
    }

    if (typeof price !== 'number' || price <= 0) {
      return NextResponse.json({ error: '价格必须为正数' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    const product = await dbService.createProduct({
      name: name.trim(),
      image: image?.trim(),
      price: Math.floor(price * 100), // 转换为分
      description: description?.trim(),
      status
    });

    return NextResponse.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('创建商品失败:', error);
    return NextResponse.json({ error: '创建商品失败' }, { status: 500 });
  }
}

// 更新商品（仅管理员）
export async function PUT(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { id, name, image, price, description, status } = body;

    if (!id) {
      return NextResponse.json({ error: '商品ID不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查商品是否存在
    const existingProduct = await dbService.getProductById(id);
    if (!existingProduct) {
      return NextResponse.json({ error: '商品不存在' }, { status: 404 });
    }

    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (image !== undefined) updateData.image = image?.trim();
    if (price !== undefined) {
      if (typeof price !== 'number' || price <= 0) {
        return NextResponse.json({ error: '价格必须为正数' }, { status: 400 });
      }
      updateData.price = Math.floor(price * 100); // 转换为分
    }
    if (description !== undefined) updateData.description = description?.trim();
    if (status !== undefined) updateData.status = status;

    const success = await dbService.updateProduct(id, updateData);

    if (!success) {
      return NextResponse.json({ error: '更新商品失败' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '商品更新成功'
    });
  } catch (error) {
    console.error('更新商品失败:', error);
    return NextResponse.json({ error: '更新商品失败' }, { status: 500 });
  }
}

// 删除商品（仅管理员）
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    // 检查管理员权限
    if (!authResult.user.role || !['super_admin', 'agent'].includes(authResult.user.role)) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: '商品ID不能为空' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查商品是否存在
    const existingProduct = await dbService.getProductById(parseInt(id));
    if (!existingProduct) {
      return NextResponse.json({ error: '商品不存在' }, { status: 404 });
    }

    const success = await dbService.deleteProduct(parseInt(id));

    if (!success) {
      return NextResponse.json({ error: '删除商品失败' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '商品删除成功'
    });
  } catch (error) {
    console.error('删除商品失败:', error);
    return NextResponse.json({ error: '删除商品失败' }, { status: 500 });
  }
}
