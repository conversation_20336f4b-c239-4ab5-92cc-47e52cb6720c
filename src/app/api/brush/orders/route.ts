import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';

// 获取用户刷单订单列表
export async function GET(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    const userId = authResult.user.id;
    const orders = await dbService.getUserBrushOrders(userId, limit);

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      ...order,
      product_price: order.product_price / 100,
      total_amount: order.total_amount / 100,
      commission_amount: order.commission_amount / 100,
      status_name: getOrderStatusName(order.status)
    }));

    return NextResponse.json({
      success: true,
      data: formattedOrders
    });
  } catch (error) {
    console.error('获取订单列表失败:', error);
    return NextResponse.json({ error: '获取订单列表失败' }, { status: 500 });
  }
}

// 创建刷单订单
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({
        error: authResult.error || '未登录'
      }, { status: authResult.status || 401 });
    }

    const body = await request.json();
    const { productId, quantity = 1 } = body;

    if (!productId) {
      return NextResponse.json({ error: '商品ID不能为空' }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({ error: '购买数量必须大于0' }, { status: 400 });
    }

    const { env } = await getCloudflareContext({ async: true });
    const dbService = getDbService(env);

    // 检查是否启用刷单功能
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return NextResponse.json({ error: '刷单功能未启用' }, { status: 403 });
    }

    const userId = authResult.user.id;

    // 检查商品是否存在且上架
    const product = await dbService.getProductById(productId);
    if (!product || product.status !== 1) {
      return NextResponse.json({ error: '商品不存在或已下架' }, { status: 404 });
    }

    // 获取活跃的刷单规则
    const brushRule = await dbService.getActiveBrushRule();
    if (!brushRule) {
      return NextResponse.json({ error: '暂无可用的刷单规则' }, { status: 500 });
    }

    // 检查用户今日订单限制
    const userStats = await dbService.getUserBrushStats(userId);
    if (userStats && userStats.today_orders >= brushRule.daily_order_limit) {
      return NextResponse.json({ error: `今日订单已达上限 ${brushRule.daily_order_limit} 单` }, { status: 400 });
    }

    // 检查下单间隔
    if (userStats && userStats.last_order_at) {
      const lastOrderTime = new Date(userStats.last_order_at).getTime();
      const now = Date.now();
      const interval = (now - lastOrderTime) / 1000; // 秒
      
      if (interval < brushRule.min_order_interval) {
        return NextResponse.json({ 
          error: `下单间隔不能少于 ${brushRule.min_order_interval} 秒` 
        }, { status: 400 });
      }
    }

    // 计算订单金额
    const totalAmount = product.price * quantity;

    // 检查用户余额
    const userBalance = await dbService.getUserBalance(userId);
    if (!userBalance || userBalance.balance < totalAmount) {
      return NextResponse.json({ error: '余额不足' }, { status: 400 });
    }

    // 生成随机佣金比例
    const commissionRate = Math.random() * (brushRule.max_commission_rate - brushRule.min_commission_rate) + brushRule.min_commission_rate;
    const commissionAmount = Math.floor(totalAmount * commissionRate);

    // 生成订单号
    const orderNo = dbService.generateOrderNo();

    // 判断是否爆单
    const userOrderCount = (userStats?.total_orders || 0) + 1;
    const [minBurst, maxBurst] = brushRule.burst_order_range.split('-').map(Number);
    const shouldBurst = userOrderCount >= minBurst && userOrderCount <= maxBurst && Math.random() < brushRule.burst_probability;

    try {
      // 创建订单
      const order = await dbService.createBrushOrder({
        userId,
        productId,
        orderNo,
        productName: product.name,
        productPrice: product.price,
        quantity,
        totalAmount,
        commissionRate,
        commissionAmount
      });

      // 扣除用户余额
      const newBalance = userBalance.balance - totalAmount;
      await dbService.updateUserBalance(userId, {
        balance: newBalance,
        totalSpent: userBalance.total_spent + totalAmount
      });

      // 创建余额变动记录
      await dbService.createBalanceLog({
        userId,
        type: 2, // 消费
        amount: totalAmount,
        balanceBefore: userBalance.balance,
        balanceAfter: newBalance,
        relatedId: order.id,
        relatedType: 'order',
        description: `购买商品: ${product.name}`
      });

      // 更新订单状态为已付款
      await dbService.updateBrushOrderStatus(order.id, 1);

      // 处理爆单或完成订单
      if (shouldBurst) {
        // 爆单处理
        await dbService.updateBrushOrderStatus(order.id, 4, true, '系统随机爆单');
        
        // 更新用户统计
        await updateUserStats(dbService, userId, userStats, true, totalAmount, 0);
      } else {
        // 正常完成订单
        await dbService.updateBrushOrderStatus(order.id, 2);
        
        // 返还本金和佣金
        const returnAmount = totalAmount + commissionAmount;
        const finalBalance = newBalance + returnAmount;
        
        await dbService.updateUserBalance(userId, {
          balance: finalBalance
        });

        // 创建佣金收入记录
        await dbService.createBalanceLog({
          userId,
          type: 3, // 佣金收入
          amount: returnAmount,
          balanceBefore: newBalance,
          balanceAfter: finalBalance,
          relatedId: order.id,
          relatedType: 'order',
          description: `订单完成返还: 本金 ${(totalAmount / 100).toFixed(2)} + 佣金 ${(commissionAmount / 100).toFixed(2)}`
        });

        // 更新用户统计
        await updateUserStats(dbService, userId, userStats, false, totalAmount, commissionAmount);
      }

      // 格式化返回数据
      const responseOrder = {
        ...order,
        product_price: order.product_price / 100,
        total_amount: order.total_amount / 100,
        commission_amount: order.commission_amount / 100,
        is_burst: shouldBurst,
        status: shouldBurst ? 4 : 2,
        status_name: shouldBurst ? '爆单' : '已完成'
      };

      return NextResponse.json({
        success: true,
        data: responseOrder,
        message: shouldBurst ? '很遗憾，本单爆单了！' : '恭喜，订单完成！'
      });

    } catch (error) {
      console.error('创建订单失败:', error);
      return NextResponse.json({ error: '创建订单失败' }, { status: 500 });
    }
  } catch (error) {
    console.error('刷单下单失败:', error);
    return NextResponse.json({ error: '刷单下单失败' }, { status: 500 });
  }
}

// 更新用户统计
async function updateUserStats(dbService: any, userId: number, currentStats: any, isBurst: boolean, totalAmount: number, commissionAmount: number) {
  const now = new Date().toISOString();
  const today = new Date().toISOString().split('T')[0];
  
  // 检查是否是今天的第一单
  const isNewDay = !currentStats || currentStats.stats_date !== today;
  
  const updateData = {
    totalOrders: (currentStats?.total_orders || 0) + 1,
    completedOrders: isBurst ? (currentStats?.completed_orders || 0) : (currentStats?.completed_orders || 0) + 1,
    burstOrders: isBurst ? (currentStats?.burst_orders || 0) + 1 : (currentStats?.burst_orders || 0),
    totalSpent: (currentStats?.total_spent || 0) + totalAmount,
    totalCommission: (currentStats?.total_commission || 0) + commissionAmount,
    todayOrders: isNewDay ? 1 : (currentStats?.today_orders || 0) + 1,
    todaySpent: isNewDay ? totalAmount : (currentStats?.today_spent || 0) + totalAmount,
    todayCommission: isNewDay ? commissionAmount : (currentStats?.today_commission || 0) + commissionAmount,
    lastOrderAt: now
  };

  await dbService.updateUserBrushStats(userId, updateData);
}

// 辅助函数：获取订单状态名称
function getOrderStatusName(status: number): string {
  const statusNames: { [key: number]: string } = {
    0: '待付款',
    1: '已付款',
    2: '已完成',
    3: '已取消',
    4: '爆单'
  };
  return statusNames[status] || '未知';
}
