import { NextRequest, NextResponse } from 'next/server';
import { authenticateUser } from '@/lib/auth-middleware';
import { getDbService } from '@/lib/db';
import { getCloudflareContext } from '@opennextjs/cloudflare';
import { createWalletService } from '@/lib/wallet-service';
import { createSuccessResponse, createErrorResponse } from '@/lib/api-response';
import { isAccountActivated, isValidTronAddress } from '@/lib/tronweb-server';

// 刷单充值转账
export async function POST(request: NextRequest) {
  try {
    const authResult = await authenticateUser(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse(
        authResult.error || '未登录',
        authResult.status || 401
      );
    }

    const body = await request.json();
    const { fromAddress, password, amount, network = 'nile' } = body as {
      fromAddress: string;
      password: string;
      amount: number;
      network?: string;
    };

    // 验证输入参数
    if (!fromAddress?.trim() || !password?.trim() || !amount) {
      return createErrorResponse('转账信息不完整', 400);
    }

    if (amount <= 0) {
      return createErrorResponse('转账金额必须大于0', 400);
    }

    const { env } = await getCloudflareContext({ async: true });
    const db = env.DB;

    // 检查是否启用刷单功能
    const dbService = getDbService(env);
    const brushEnabledConfig = await dbService.getSystemConfig('brush_enabled');
    if (!brushEnabledConfig || brushEnabledConfig.config_value !== 'true') {
      return createErrorResponse('刷单功能未启用', 403);
    }

    // 获取平台收款地址
    const platformWallet = await dbService.getDefaultPlatformWallet(network, 'USDT');
    if (!platformWallet) {
      return createErrorResponse('暂无可用的收款地址', 500);
    }

    // 使用平台钱包地址作为收款地址
    const toAddress = platformWallet.address;

    // 检查最小充值金额
    const minRechargeConfig = await dbService.getSystemConfig('brush_min_recharge');
    const minRecharge = minRechargeConfig ? parseFloat(minRechargeConfig.config_value) : 10;

    if (amount < minRecharge) {
      return createErrorResponse(`充值金额不能少于 ${minRecharge} USDT`, 400);
    }

    // 验证用户拥有该钱包
    const wallet = await db.prepare(`
      SELECT id, name, address, private_key_encrypted, network
      FROM wallets
      WHERE address = ? AND user_id = ? AND is_active = TRUE
    `).bind(fromAddress.trim(), authResult.user.id).first();

    if (!wallet) {
      return createErrorResponse('钱包不存在或无权限', 404);
    }

    // 检查发送账户是否已激活
    const isActivated = await isAccountActivated(fromAddress.trim(), network as 'mainnet' | 'nile' | 'shasta');
    if (!isActivated) {
      return createErrorResponse(
        `钱包地址 ${fromAddress.trim()} 尚未激活。在TRON网络中，新创建的账户需要先接收一笔转账才能被激活。请先向此地址转入一些TRX来激活账户。`,
        400
      );
    }

    // 执行转账
    console.log(`执行刷单充值转账 - 网络: ${network}, 从: ${fromAddress} 到: ${toAddress}, 金额: ${amount} USDT`);

    const walletService = createWalletService(network as 'mainnet' | 'nile' | 'shasta');
    const transferResult = await walletService.transferUSDT(
      fromAddress.trim(),
      password,
      toAddress.trim(),
      amount,
      db
    );

    if (transferResult.success && transferResult.txHash) {
      // 记录交易到数据库
      await db.prepare(`
        INSERT INTO transactions (
          user_id, wallet_id, tx_hash, from_address, to_address,
          amount, currency, tx_type, status, network, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'brush_recharge', 'pending', ?, ?)
      `).bind(
        authResult.user.id,
        wallet.id,
        transferResult.txHash,
        fromAddress.trim(),
        toAddress.trim(),
        amount,
        'USDT',
        network,
        new Date().toISOString()
      ).run();

      console.log(`转账成功 - 交易哈希: ${transferResult.txHash}`);

      // 异步等待交易确认并自动创建充值记录
      const userId = authResult.user!.id; // 已经验证过用户存在
      setTimeout(async () => {
        try {
          const confirmed = await walletService.waitForConfirmation(transferResult.txHash!, 300000); // 5分钟超时

          if (confirmed) {
            // 更新交易状态
            await db.prepare(`
              UPDATE transactions
              SET status = 'confirmed', confirmed_at = ?
              WHERE tx_hash = ?
            `).bind(
              new Date().toISOString(),
              transferResult.txHash
            ).run();

            // 自动创建充值记录
            await db.prepare(`
              INSERT INTO brush_recharge_records (
                user_id, tx_hash, amount, from_address, to_address,
                network, status, created_at, confirmed_at
              ) VALUES (?, ?, ?, ?, ?, ?, 'confirmed', ?, ?)
            `).bind(
              userId,
              transferResult.txHash,
              amount,
              fromAddress.trim(),
              toAddress.trim(),
              network,
              new Date().toISOString(),
              new Date().toISOString()
            ).run();

            // 增加用户刷单余额（直接操作数据库）
            await db.prepare(`
              INSERT INTO brush_balances (user_id, balance, created_at, updated_at)
              VALUES (?, ?, ?, ?)
              ON CONFLICT(user_id) DO UPDATE SET
                balance = balance + ?,
                updated_at = ?
            `).bind(
              userId,
              amount,
              new Date().toISOString(),
              new Date().toISOString(),
              amount,
              new Date().toISOString()
            ).run();

            console.log(`充值确认完成 - 用户: ${userId}, 金额: ${amount} USDT`);
          } else {
            // 交易失败
            await db.prepare(`
              UPDATE transactions
              SET status = 'failed'
              WHERE tx_hash = ?
            `).bind(transferResult.txHash).run();

            console.log(`交易确认失败 - 交易哈希: ${transferResult.txHash}`);
          }
        } catch (error) {
          console.error('处理交易确认失败:', error);
        }
      }, 0);

      return createSuccessResponse({
        txHash: transferResult.txHash,
        amount: amount,
        fromAddress: fromAddress.trim(),
        toAddress: toAddress.trim(),
        message: '转账已提交，请等待区块链确认'
      });
    } else {
      return createErrorResponse(
        transferResult.error || '转账失败',
        500
      );
    }

  } catch (error: any) {
    console.error('刷单充值转账失败:', error);
    return createErrorResponse(
      error.message || '转账失败',
      500
    );
  }
}
