'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/SessionProvider';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Chip,
  Spacer,
  Spinner,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Select,
  SelectItem,
  Tabs,
  Tab,
  Divider
} from "@heroui/react";
import QRCodeComponent from '@/components/QRCode';
import MobileInput from '@/components/MobileInput';
import { createWalletService, type WalletInfo, type WalletBalance } from '@/lib/wallet-service';

interface UserWallet {
  id: number;
  name: string;
  address: string;
  wallet_type: 'created' | 'imported';
  network?: 'mainnet' | 'nile' | 'shasta';
  is_default: boolean;
  balance?: WalletBalance;
}

// 安全的余额格式化函数
const formatBalance = (balance: number | string | undefined, decimals: number = 6): string => {
  if (balance === undefined || balance === null) {
    return '0.' + '0'.repeat(decimals);
  }

  const numBalance = typeof balance === 'string' ? parseFloat(balance) : balance;

  if (isNaN(numBalance)) {
    return '0.' + '0'.repeat(decimals);
  }

  return numBalance.toFixed(decimals);
};

export default function WalletPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [wallets, setWallets] = useState<UserWallet[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedWallet, setSelectedWallet] = useState<UserWallet | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showReceiveModal, setShowReceiveModal] = useState(false);
  
  // 创建钱包表单
  const [createForm, setCreateForm] = useState({
    name: '',
    network: 'nile',
    password: ''
  });
  
  // 导入钱包表单
  const [importForm, setImportForm] = useState({
    name: '',
    privateKey: '',
    network: 'nile',
    password: ''
  });
  
  // 转账表单
  const [transferForm, setTransferForm] = useState({
    toAddress: '',
    amount: '',
    currency: 'TRX' as 'TRX' | 'USDT',
    password: ''
  });

  const [creating, setCreating] = useState(false);
  const [importing, setImporting] = useState(false);
  const [transferring, setTransferring] = useState(false);

  // 检查登录状态
  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/auth/signin');
      return;
    }

    loadWallets();
  }, [user, authLoading, router]);

  // 加载用户钱包
  const loadWallets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/wallets');
      if (response.ok) {
        const result = await response.json() as { success: boolean; data?: UserWallet[]; error?: string };
        if (result.success && result.data) {
          const walletsData = result.data;
          setWallets(walletsData);

          // 设置默认选中钱包
          const defaultWallet = walletsData.find((w: UserWallet) => w.is_default) || walletsData[0];
          if (defaultWallet) {
            setSelectedWallet(defaultWallet);
            await loadWalletBalance(defaultWallet);
          }
        } else {
          console.error('获取钱包列表失败:', result.error);
        }
      }
    } catch (error) {
      console.error('加载钱包失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载钱包余额
  const loadWalletBalance = async (wallet: UserWallet) => {
    try {
      const network = wallet.network || 'nile'; // 默认使用Nile测试网

      // 使用服务端API获取余额
      const trxResponse = await fetch('/api/wallets/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: wallet.address,
          currency: 'TRX',
          network: network
        }),
      });

      const usdtResponse = await fetch('/api/wallets/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: wallet.address,
          currency: 'USDT',
          network: network
        }),
      });

      let trxBalance = 0;
      let usdtBalance = 0;

      if (trxResponse.ok) {
        const trxData = await trxResponse.json() as { balance?: number };
        trxBalance = trxData.balance || 0;
      }

      if (usdtResponse.ok) {
        const usdtData = await usdtResponse.json() as { balance?: number };
        usdtBalance = usdtData.balance || 0;
      }

      const balance = {
        TRX: trxBalance,
        USDT: usdtBalance
      };

      console.log(`钱包 ${wallet.address} 余额更新:`, balance);

      setWallets(prev => prev.map(w =>
        w.id === wallet.id ? { ...w, balance } : w
      ));

      if (selectedWallet?.id === wallet.id) {
        setSelectedWallet({ ...wallet, balance });
        console.log('选中钱包余额已更新:', { ...wallet, balance });
      }
    } catch (error) {
      console.error('获取余额失败:', error);
    }
  };

  // 创建钱包
  const handleCreateWallet = async () => {
    if (!createForm.name.trim()) {
      alert('请输入钱包名称');
      return;
    }

    if (!createForm.password.trim()) {
      alert('请设置钱包密码');
      return;
    }

    if (createForm.password.length < 6) {
      alert('密码长度至少6位');
      return;
    }

    try {
      setCreating(true);
      const response = await fetch('/api/wallets/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(createForm)
      });

      if (response.ok) {
        const newWallet = await response.json() as { address: string };
        alert(`钱包创建成功！\n\n地址: ${newWallet.address}\n\n请务必安全保存您的私钥！`);
        setShowCreateModal(false);
        setCreateForm({ name: '', network: 'nile', password: '' });
        await loadWallets();
      } else {
        const error = await response.json() as { message: string };
        alert(`创建失败: ${error.message}`);
      }
    } catch (error) {
      console.error('创建钱包失败:', error);
      alert('创建钱包失败，请重试');
    } finally {
      setCreating(false);
    }
  };

  // 导入钱包
  const handleImportWallet = async () => {
    if (!importForm.name.trim() || !importForm.privateKey.trim()) {
      alert('请填写完整信息');
      return;
    }

    if (!importForm.password.trim()) {
      alert('请设置钱包密码');
      return;
    }

    if (importForm.password.length < 6) {
      alert('密码长度至少6位');
      return;
    }

    try {
      setImporting(true);
      const response = await fetch('/api/wallets/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(importForm)
      });

      if (response.ok) {
        alert('钱包导入成功！');
        setShowImportModal(false);
        setImportForm({ name: '', privateKey: '', network: 'nile', password: '' });
        await loadWallets();
      } else {
        const error = await response.json() as { message: string };
        alert(`导入失败: ${error.message}`);
      }
    } catch (error) {
      console.error('导入钱包失败:', error);
      alert('导入钱包失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  // 转账
  const handleTransfer = async () => {
    if (!selectedWallet || !transferForm.toAddress.trim() || !transferForm.amount.trim()) {
      alert('请填写完整的转账信息');
      return;
    }

    try {
      setTransferring(true);
      const response = await fetch('/api/wallets/transfer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          walletId: selectedWallet.id,
          ...transferForm,
          amount: parseFloat(transferForm.amount)
        })
      });

      if (response.ok) {
        const result = await response.json() as { success: boolean; txHash?: string; error?: string };
        if (result.success) {
          alert(`转账成功！\n交易哈希: ${result.txHash}`);
          setShowTransferModal(false);
          setTransferForm({ toAddress: '', amount: '', currency: 'TRX', password: '' });
          await loadWalletBalance(selectedWallet);
        } else {
          alert(`转账失败: ${result.error}`);
        }
      } else {
        const error = await response.json() as { message: string };
        alert(`转账失败: ${error.message}`);
      }
    } catch (error) {
      console.error('转账失败:', error);
      alert('转账失败，请重试');
    } finally {
      setTransferring(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">TRON钱包</h1>
            <p className="text-gray-600 mt-1">安全、便捷的数字资产管理</p>
          </div>
          <div className="flex gap-3">
            <Button
              color="warning"
              variant="solid"
              onPress={() => router.push('/brush')}
              className="font-semibold"
            >
              🎯 刷单赚钱
            </Button>
            <Button
              color="primary"
              variant="solid"
              onPress={() => setShowCreateModal(true)}
            >
              创建钱包
            </Button>
            <Button
              color="secondary"
              variant="bordered"
              onPress={() => setShowImportModal(true)}
            >
              导入钱包
            </Button>
          </div>
        </div>

        {/* 钱包列表和余额 */}
        {wallets.length === 0 ? (
          <Card className="p-8 text-center">
            <CardBody>
              <h3 className="text-xl font-semibold mb-4">还没有钱包</h3>
              <p className="text-gray-600 mb-6">创建或导入您的第一个TRON钱包开始使用</p>
              <div className="flex gap-4 justify-center">
                <Button
                  color="primary"
                  size="lg"
                  onPress={() => setShowCreateModal(true)}
                >
                  创建新钱包
                </Button>
                <Button
                  color="secondary"
                  variant="bordered"
                  size="lg"
                  onPress={() => setShowImportModal(true)}
                >
                  导入现有钱包
                </Button>
              </div>
            </CardBody>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 钱包列表 */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">我的钱包</h3>
                </CardHeader>
                <CardBody className="space-y-3">
                  {wallets.map((wallet) => (
                    <div
                      key={wallet.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedWallet?.id === wallet.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => {
                        setSelectedWallet(wallet);
                        loadWalletBalance(wallet);
                      }}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{wallet.name}</p>
                          <p className="text-sm text-gray-500">
                            {wallet.address.slice(0, 8)}...{wallet.address.slice(-6)}
                          </p>
                          <p className="text-xs text-blue-600">
                            {wallet.network === 'mainnet' ? '主网' :
                             wallet.network === 'nile' ? 'Nile测试网' :
                             wallet.network === 'shasta' ? 'Shasta测试网' : 'Nile测试网'}
                          </p>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          <Chip
                            size="sm"
                            color={wallet.wallet_type === 'created' ? 'success' : 'primary'}
                            variant="flat"
                          >
                            {wallet.wallet_type === 'created' ? '已创建' : '已导入'}
                          </Chip>
                          {wallet.is_default && (
                            <Chip size="sm" color="warning" variant="flat">
                              默认
                            </Chip>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardBody>
              </Card>
            </div>

            {/* 钱包详情和操作 */}
            <div className="lg:col-span-2">
              {selectedWallet && (
                <div className="space-y-6">
                  {/* 余额卡片 */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">钱包余额</h3>
                    </CardHeader>
                    <CardBody>
                      <div className="grid grid-cols-2 gap-6">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-red-600">
                            {formatBalance(selectedWallet.balance?.TRX, 6)} TRX
                          </p>
                          <p className="text-sm text-gray-500">TRON</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">
                            {formatBalance(selectedWallet.balance?.USDT, 2)} USDT
                          </p>
                          <p className="text-sm text-gray-500">Tether USD</p>
                        </div>
                      </div>
                      <Spacer y={4} />
                      <div className="flex gap-4 justify-center">
                        <Button
                          color="primary"
                          size="lg"
                          onPress={() => setShowTransferModal(true)}
                        >
                          转账
                        </Button>
                        <Button
                          color="success"
                          variant="bordered"
                          size="lg"
                          onPress={() => setShowReceiveModal(true)}
                        >
                          收款
                        </Button>
                        <Button
                          color="default"
                          variant="bordered"
                          size="lg"
                          onPress={() => loadWalletBalance(selectedWallet)}
                        >
                          刷新
                        </Button>
                      </div>
                    </CardBody>
                  </Card>

                  {/* 钱包信息 */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">钱包信息</h3>
                    </CardHeader>
                    <CardBody>
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-gray-500">钱包名称</p>
                          <p className="font-medium">{selectedWallet.name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">钱包地址</p>
                          <p className="font-mono text-sm break-all">{selectedWallet.address}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">钱包类型</p>
                          <p className="font-medium">
                            {selectedWallet.wallet_type === 'created' ? '创建的钱包' : '导入的钱包'}
                          </p>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 创建钱包模态框 */}
        <Modal isOpen={showCreateModal} onClose={() => setShowCreateModal(false)}>
          <ModalContent>
            <ModalHeader>创建新钱包</ModalHeader>
            <ModalBody>
              <Input
                label="钱包名称"
                placeholder="输入钱包名称"
                value={createForm.name}
                onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
              />
              <Input
                label="钱包密码"
                type="password"
                placeholder="设置钱包密码（用于转账时验证）"
                value={createForm.password}
                onChange={(e) => setCreateForm({ ...createForm, password: e.target.value })}
                description="密码用于保护您的私钥，转账时需要输入此密码"
              />
              <Select
                label="网络"
                selectedKeys={[createForm.network]}
                onSelectionChange={(keys) =>
                  setCreateForm({ ...createForm, network: Array.from(keys)[0] as string })
                }
              >
                <SelectItem key="mainnet">主网 (Mainnet)</SelectItem>
                <SelectItem key="nile">测试网 (Nile)</SelectItem>
              </Select>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setShowCreateModal(false)}>
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleCreateWallet}
                isLoading={creating}
              >
                创建钱包
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 导入钱包模态框 */}
        <Modal isOpen={showImportModal} onClose={() => setShowImportModal(false)}>
          <ModalContent>
            <ModalHeader>导入钱包</ModalHeader>
            <ModalBody>
              <Input
                label="钱包名称"
                placeholder="输入钱包名称"
                value={importForm.name}
                onChange={(e) => setImportForm({ ...importForm, name: e.target.value })}
              />
              <Input
                label="私钥"
                placeholder="输入64位私钥"
                type="password"
                value={importForm.privateKey}
                onChange={(e) => setImportForm({ ...importForm, privateKey: e.target.value })}
              />
              <Input
                label="钱包密码"
                type="password"
                placeholder="设置钱包密码（用于转账时验证）"
                value={importForm.password}
                onChange={(e) => setImportForm({ ...importForm, password: e.target.value })}
                description="密码用于保护您的私钥，转账时需要输入此密码"
              />
              <Select
                label="网络"
                selectedKeys={[importForm.network]}
                onSelectionChange={(keys) =>
                  setImportForm({ ...importForm, network: Array.from(keys)[0] as string })
                }
              >
                <SelectItem key="mainnet">主网 (Mainnet)</SelectItem>
                <SelectItem key="nile">测试网 (Nile)</SelectItem>
              </Select>
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setShowImportModal(false)}>
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleImportWallet}
                isLoading={importing}
              >
                导入钱包
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 转账模态框 */}
        <Modal isOpen={showTransferModal} onClose={() => setShowTransferModal(false)}>
          <ModalContent>
            <ModalHeader>发送转账</ModalHeader>
            <ModalBody>
              <MobileInput
                label="接收地址"
                placeholder="输入TRON地址"
                value={transferForm.toAddress}
                onChange={(e) => setTransferForm({ ...transferForm, toAddress: e.target.value })}
              />
              <div className="flex gap-3">
                <MobileInput
                  label="金额"
                  placeholder="输入转账金额"
                  type="number"
                  value={transferForm.amount}
                  onChange={(e) => setTransferForm({ ...transferForm, amount: e.target.value })}
                />
                <Select
                  label="币种"
                  selectedKeys={[transferForm.currency]}
                  onSelectionChange={(keys) => 
                    setTransferForm({ ...transferForm, currency: Array.from(keys)[0] as 'TRX' | 'USDT' })
                  }
                >
                  <SelectItem key="TRX">TRX</SelectItem>
                  <SelectItem key="USDT">USDT</SelectItem>
                </Select>
              </div>
              <MobileInput
                label="钱包密码"
                placeholder="输入钱包密码"
                type="password"
                value={transferForm.password}
                onChange={(e) => setTransferForm({ ...transferForm, password: e.target.value })}
              />
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setShowTransferModal(false)}>
                取消
              </Button>
              <Button
                color="primary"
                onPress={handleTransfer}
                isLoading={transferring}
              >
                确认转账
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* 收款模态框 */}
        <Modal isOpen={showReceiveModal} onClose={() => setShowReceiveModal(false)}>
          <ModalContent>
            <ModalHeader>收款</ModalHeader>
            <ModalBody>
              {selectedWallet && (
                <div className="text-center space-y-4">
                  <p className="text-lg font-semibold">扫描二维码收款</p>
                  <div className="bg-white p-4 rounded-lg border flex justify-center">
                    <QRCodeComponent
                      value={selectedWallet.address}
                      size={200}
                      className="mx-auto"
                    />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">钱包地址</p>
                    <p className="font-mono text-sm break-all bg-gray-100 p-2 rounded">
                      {selectedWallet.address}
                    </p>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={() => setShowReceiveModal(false)}>
                关闭
              </Button>
              <Button
                color="primary"
                onPress={() => {
                  if (selectedWallet) {
                    navigator.clipboard.writeText(selectedWallet.address);
                    alert('地址已复制到剪贴板');
                  }
                }}
              >
                复制地址
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
}
