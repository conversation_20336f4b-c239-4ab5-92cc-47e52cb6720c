'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  <PERSON>,
  Spin<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab
} from "@heroui/react";

interface BrushOrder {
  id: number;
  order_no: string;
  product_name: string;
  product_price: number;
  quantity: number;
  total_amount: number;
  commission_rate: number;
  commission_amount: number;
  status: number;
  status_name: string;
  is_burst: boolean;
  burst_reason?: string;
  completed_at?: string;
  created_at: string;
}

interface BalanceLog {
  id: number;
  type: number;
  type_name: string;
  amount: number;
  balance_before: number;
  balance_after: number;
  description?: string;
  created_at: string;
}

interface RechargeRecord {
  id: number;
  tx_hash: string;
  from_address: string;
  to_address: string;
  usdt_amount: number;
  internal_amount: number;
  exchange_rate: number;
  status: number;
  status_name: string;
  network: string;
  created_at: string;
  confirmed_at?: string;
}

export default function BrushOrderList() {
  const [orders, setOrders] = useState<BrushOrder[]>([]);
  const [balanceLogs, setBalanceLogs] = useState<BalanceLog[]>([]);
  const [rechargeRecords, setRechargeRecords] = useState<RechargeRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('orders');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadOrders(),
        loadBalanceLogs(),
        loadRechargeRecords()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadOrders = async () => {
    try {
      const response = await fetch('/api/brush/orders');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setOrders(result.data);
        }
      }
    } catch (error) {
      console.error('加载订单失败:', error);
    }
  };

  const loadBalanceLogs = async () => {
    try {
      const response = await fetch('/api/brush/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'logs' }),
      });
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBalanceLogs(result.data);
        }
      }
    } catch (error) {
      console.error('加载余额记录失败:', error);
    }
  };

  const loadRechargeRecords = async () => {
    try {
      const response = await fetch('/api/brush/balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'recharge_records' }),
      });
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setRechargeRecords(result.data);
        }
      }
    } catch (error) {
      console.error('加载充值记录失败:', error);
    }
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'warning'; // 待付款
      case 1: return 'primary'; // 已付款
      case 2: return 'success'; // 已完成
      case 3: return 'default'; // 已取消
      case 4: return 'danger';  // 爆单
      default: return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
        className="w-full"
      >
        <Tab key="orders" title={`刷单订单 (${orders.length})`}>
          <div className="space-y-4 mt-4">
            {orders.length === 0 ? (
              <Card>
                <CardBody className="text-center py-8">
                  <p className="text-gray-500">暂无刷单订单</p>
                </CardBody>
              </Card>
            ) : (
              orders.map((order) => (
                <Card key={order.id}>
                  <CardBody>
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <p className="font-semibold">{order.product_name}</p>
                        <p className="text-sm text-gray-500">订单号: {order.order_no}</p>
                      </div>
                      <Chip
                        color={getStatusColor(order.status)}
                        variant="flat"
                        size="sm"
                      >
                        {order.status_name}
                      </Chip>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">商品价格: </span>
                        <span>¥{order.product_price.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">购买数量: </span>
                        <span>{order.quantity}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">订单金额: </span>
                        <span className="font-semibold">¥{order.total_amount.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">佣金: </span>
                        <span className="text-green-600">¥{order.commission_amount.toFixed(2)}</span>
                      </div>
                    </div>

                    {order.is_burst && order.burst_reason && (
                      <div className="mt-3 p-2 bg-red-50 rounded-lg">
                        <p className="text-sm text-red-700">
                          💥 爆单原因: {order.burst_reason}
                        </p>
                      </div>
                    )}

                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        创建时间: {formatDate(order.created_at)}
                        {order.completed_at && (
                          <span className="ml-4">
                            完成时间: {formatDate(order.completed_at)}
                          </span>
                        )}
                      </p>
                    </div>
                  </CardBody>
                </Card>
              ))
            )}
          </div>
        </Tab>

        <Tab key="balance" title={`余额记录 (${balanceLogs.length})`}>
          <div className="space-y-4 mt-4">
            {balanceLogs.length === 0 ? (
              <Card>
                <CardBody className="text-center py-8">
                  <p className="text-gray-500">暂无余额变动记录</p>
                </CardBody>
              </Card>
            ) : (
              balanceLogs.map((log) => (
                <Card key={log.id}>
                  <CardBody>
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Chip
                            color={log.type === 1 ? 'success' : log.type === 3 ? 'primary' : 'warning'}
                            variant="flat"
                            size="sm"
                          >
                            {log.type_name}
                          </Chip>
                          <span className={`font-semibold ${log.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {log.amount > 0 ? '+' : ''}¥{log.amount.toFixed(2)}
                          </span>
                        </div>
                        {log.description && (
                          <p className="text-sm text-gray-600 mb-2">{log.description}</p>
                        )}
                        <div className="text-xs text-gray-500">
                          <span>余额: ¥{log.balance_before.toFixed(2)} → ¥{log.balance_after.toFixed(2)}</span>
                          <span className="ml-4">{formatDate(log.created_at)}</span>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))
            )}
          </div>
        </Tab>

        <Tab key="recharge" title={`充值记录 (${rechargeRecords.length})`}>
          <div className="space-y-4 mt-4">
            {rechargeRecords.length === 0 ? (
              <Card>
                <CardBody className="text-center py-8">
                  <p className="text-gray-500">暂无充值记录</p>
                </CardBody>
              </Card>
            ) : (
              rechargeRecords.map((record) => (
                <Card key={record.id}>
                  <CardBody>
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <p className="font-semibold">USDT充值</p>
                        <p className="text-sm text-gray-500 font-mono">
                          {record.tx_hash.slice(0, 10)}...{record.tx_hash.slice(-8)}
                        </p>
                      </div>
                      <Chip
                        color={record.status === 1 ? 'success' : record.status === 2 ? 'danger' : 'warning'}
                        variant="flat"
                        size="sm"
                      >
                        {record.status_name}
                      </Chip>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">USDT金额: </span>
                        <span>{record.usdt_amount} USDT</span>
                      </div>
                      <div>
                        <span className="text-gray-500">内部金额: </span>
                        <span className="font-semibold">¥{record.internal_amount.toFixed(2)}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">汇率: </span>
                        <span>1:{record.exchange_rate}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">网络: </span>
                        <span>{record.network}</span>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        提交时间: {formatDate(record.created_at)}
                        {record.confirmed_at && (
                          <span className="ml-4">
                            确认时间: {formatDate(record.confirmed_at)}
                          </span>
                        )}
                      </p>
                    </div>
                  </CardBody>
                </Card>
              ))
            )}
          </div>
        </Tab>
      </Tabs>
    </div>
  );
}
