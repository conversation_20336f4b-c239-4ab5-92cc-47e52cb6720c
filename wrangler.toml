name = "amazon-tk-system"
compatibility_date = "2024-01-01"

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "amazon-tk-system-db"
database_id = "your-d1-database-id"

# KV 存储绑定（用于邮箱发送频率限制）
[[kv_namespaces]]
binding = "KV"
id = "rate_limit_kv"
preview_id = "rate_limit_kv_preview"

# 环境变量
[vars]
NEXTAUTH_URL = "http://localhost:3000"
NEXTAUTH_SECRET = "your-nextauth-secret"

# 开发环境配置
[env.development]
vars = { NODE_ENV = "development" }

# 生产环境配置
[env.production]
vars = { NODE_ENV = "production" }
